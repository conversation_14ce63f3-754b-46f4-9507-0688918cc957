{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function formatTime(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function getInitials(firstName: string, lastName: string): string {\n  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // UUID v4 regex\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(sessionId);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,YAAY,SAAiB,EAAE,QAAgB;IAC7D,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;AAClE;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,iBAAiB,SAAiB;IAChD,gBAAgB;IAChB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';\n    \n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow',\n      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow',\n      outline: 'border border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground shadow-sm',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',\n      ghost: 'text-foreground hover:bg-accent hover:text-accent-foreground',\n      link: 'underline-offset-4 hover:underline text-primary',\n    };\n\n    const sizes = {\n      default: 'h-10 py-2 px-4',\n      sm: 'h-9 px-3 rounded-md',\n      lg: 'h-11 px-8 rounded-md',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/Button';\nimport { \n  HeartIcon, \n  UserIcon, \n  ChatBubbleLeftIcon, \n  ClipboardDocumentListIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { getInitials } from '@/lib/utils';\n\nexport function Navbar() {\n  const { user, logout, isAuthenticated } = useAuth();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: HeartIcon },\n    { name: 'Chat', href: '/chat', icon: ChatBubbleLeftIcon },\n    { name: 'Treatment', href: '/treatment', icon: ClipboardDocumentListIcon },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg\">\n                <HeartIcon className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">ATHLIX</span>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          {isAuthenticated && (\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-1 text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              ))}\n            </div>\n          )}\n\n          {/* User menu */}\n          <div className=\"flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-3\">\n                {/* User avatar */}\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-blue-600\">\n                      {user ? getInitials(user.first_name, user.last_name) : 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm text-gray-700\">\n                    {user?.first_name} {user?.last_name}\n                  </span>\n                </div>\n                \n                {/* Logout button */}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={logout}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"w-4 h-4\" />\n                  <span className=\"hidden sm:ml-2 sm:block\">Logout</span>\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    Login\n                  </Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button size=\"sm\">\n                    Sign Up\n                  </Button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            {isAuthenticated && (\n              <div className=\"md:hidden\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                >\n                  {mobileMenuOpen ? (\n                    <XMarkIcon className=\"w-5 h-5\" />\n                  ) : (\n                    <Bars3Icon className=\"w-5 h-5\" />\n                  )}\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isAuthenticated && mobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <item.icon className=\"w-5 h-5\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAfA;;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,iNAAA,CAAA,YAAS;QAAC;QACzD;YAAE,MAAM;YAAQ,MAAM;YAAS,MAAM,mOAAA,CAAA,qBAAkB;QAAC;QACxD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,iPAAA,CAAA,4BAAyB;QAAC;KAC1E;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;wBAKrD,iCACC,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAYtB,8OAAC;4BAAI,WAAU;;gCACZ,gCACC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,OAAO,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;8DAG3D,8OAAC;oDAAK,WAAU;;wDACb,MAAM;wDAAW;wDAAE,MAAM;;;;;;;;;;;;;sDAK9B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,iPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;8DACrC,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;yDAI9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAAK;;;;;;;;;;;sDAIpC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;gCAQvB,iCACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,kBAAkB,CAAC;kDAEjC,+BACC,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,mBAAmB,gCAClB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAM,KAAK,IAAI;;;;;;;2BANX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAc9B", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border border-border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight text-card-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Navbar } from '@/components/layout/Navbar';\nimport { Button } from '@/components/ui/Button';\nimport { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';\nimport { \n  HeartIcon, \n  ChatBubbleLeftIcon, \n  ClipboardDocumentListIcon,\n  UserIcon,\n  CalendarIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\nimport { formatDate } from '@/lib/utils';\nimport { systemAPI, chatAPI } from '@/lib/api';\n\nexport default function DashboardPage() {\n  const { user, isAuthenticated } = useAuth();\n  const [healthStatus, setHealthStatus] = useState<any>(null);\n  const [recentChats, setRecentChats] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      window.location.href = '/login';\n      return;\n    }\n\n    const fetchDashboardData = async () => {\n      try {\n        // Fetch health status\n        const health = await systemAPI.getHealth();\n        setHealthStatus(health);\n\n        // Fetch recent chat sessions\n        const chats = await chatAPI.getSessions();\n        setRecentChats(chats.sessions?.slice(0, 3) || []);\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, [isAuthenticated]);\n\n  const quickActions = [\n    {\n      title: 'Start New Chat',\n      description: 'Get instant health advice from our AI assistant',\n      icon: ChatBubbleLeftIcon,\n      href: '/chat',\n      color: 'bg-blue-500',\n    },\n    {\n      title: 'Treatment Assessment',\n      description: 'Upload images and get personalized treatment plans',\n      icon: ClipboardDocumentListIcon,\n      href: '/treatment',\n      color: 'bg-green-500',\n    },\n    {\n      title: 'Health Profile',\n      description: 'Manage your health information and preferences',\n      icon: UserIcon,\n      href: '/profile',\n      color: 'bg-purple-500',\n    },\n  ];\n\n  const stats = [\n    {\n      name: 'Total Users',\n      value: healthStatus?.users_count || 0,\n      icon: UserIcon,\n      change: '+12%',\n      changeType: 'positive',\n    },\n    {\n      name: 'Active Chats',\n      value: healthStatus?.active_chats || 0,\n      icon: ChatBubbleLeftIcon,\n      change: '+5%',\n      changeType: 'positive',\n    },\n    {\n      name: 'Treatment Sessions',\n      value: healthStatus?.treatment_sessions || 0,\n      icon: ClipboardDocumentListIcon,\n      change: '+8%',\n      changeType: 'positive',\n    },\n  ];\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            Welcome back, {user?.first_name}!\n          </h1>\n          <p className=\"mt-2 text-gray-600\">\n            Here's your health dashboard overview. How can ATHLIX help you today?\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          {stats.map((stat) => (\n            <Card key={stat.name}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <stat.icon className=\"h-8 w-8 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                    <div className=\"flex items-baseline\">\n                      <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                      <p className={`ml-2 text-sm font-medium ${\n                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {stat.change}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Quick Actions</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {quickActions.map((action) => (\n              <Link key={action.title} href={action.href}>\n                <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-center\">\n                      <div className={`flex-shrink-0 p-3 rounded-lg ${action.color}`}>\n                        <action.icon className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div className=\"ml-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900\">{action.title}</h3>\n                        <p className=\"text-sm text-gray-500\">{action.description}</p>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </Link>\n            ))}\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Chats */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <ChatBubbleLeftIcon className=\"h-5 w-5 mr-2\" />\n                Recent Chats\n              </CardTitle>\n              <CardDescription>Your latest conversations with ATHLIX</CardDescription>\n            </CardHeader>\n            <CardContent>\n              {loading ? (\n                <div className=\"space-y-3\">\n                  {[1, 2, 3].map((i) => (\n                    <div key={i} className=\"animate-pulse\">\n                      <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                    </div>\n                  ))}\n                </div>\n              ) : recentChats.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {recentChats.map((chat) => (\n                    <div key={chat.session_id} className=\"border-b border-gray-200 pb-3 last:border-b-0\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            Chat Session\n                          </p>\n                          <p className=\"text-xs text-gray-500\">\n                            {chat.message_count} messages\n                          </p>\n                        </div>\n                        <div className=\"text-xs text-gray-400\">\n                          {chat.last_activity ? formatDate(chat.last_activity) : 'No activity'}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                  <Link href=\"/chat\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                      View All Chats\n                    </Button>\n                  </Link>\n                </div>\n              ) : (\n                <div className=\"text-center py-6\">\n                  <ChatBubbleLeftIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No chats yet</h3>\n                  <p className=\"mt-1 text-sm text-gray-500\">Start your first conversation with ATHLIX</p>\n                  <div className=\"mt-6\">\n                    <Link href=\"/chat\">\n                      <Button size=\"sm\">Start Chat</Button>\n                    </Link>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Health Tips */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <HeartIcon className=\"h-5 w-5 mr-2\" />\n                Health Tips\n              </CardTitle>\n              <CardDescription>Daily recommendations for better health</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"p-4 bg-blue-50 rounded-lg\">\n                  <h4 className=\"text-sm font-medium text-blue-900\">Stay Hydrated</h4>\n                  <p className=\"text-sm text-blue-700 mt-1\">\n                    Drink at least 8 glasses of water daily to maintain optimal health.\n                  </p>\n                </div>\n                <div className=\"p-4 bg-green-50 rounded-lg\">\n                  <h4 className=\"text-sm font-medium text-green-900\">Regular Exercise</h4>\n                  <p className=\"text-sm text-green-700 mt-1\">\n                    Aim for 30 minutes of moderate exercise 5 times a week.\n                  </p>\n                </div>\n                <div className=\"p-4 bg-purple-50 rounded-lg\">\n                  <h4 className=\"text-sm font-medium text-purple-900\">Quality Sleep</h4>\n                  <p className=\"text-sm text-purple-700 mt-1\">\n                    Get 7-9 hours of quality sleep each night for optimal recovery.\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAQA;AACA;AAjBA;;;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB;YACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACvB;QACF;QAEA,MAAM,qBAAqB;YACzB,IAAI;gBACF,sBAAsB;gBACtB,MAAM,SAAS,MAAM,iHAAA,CAAA,YAAS,CAAC,SAAS;gBACxC,gBAAgB;gBAEhB,6BAA6B;gBAC7B,MAAM,QAAQ,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW;gBACvC,eAAe,MAAM,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,mOAAA,CAAA,qBAAkB;YACxB,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,iPAAA,CAAA,4BAAyB;YAC/B,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,+MAAA,CAAA,WAAQ;YACd,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO,cAAc,eAAe;YACpC,MAAM,+MAAA,CAAA,WAAQ;YACd,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO,cAAc,gBAAgB;YACrC,MAAM,mOAAA,CAAA,qBAAkB;YACxB,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO,cAAc,sBAAsB;YAC3C,MAAM,iPAAA,CAAA,4BAAyB;YAC/B,QAAQ;YACR,YAAY;QACd;KACD;IAED,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAmC;oCAChC,MAAM;oCAAW;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAMpC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,KAAK,IAAI;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAwC,KAAK,KAAK;;;;;;0EAC/D,8OAAC;gEAAE,WAAW,CAAC,yBAAyB,EACtC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;0EACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAbb,KAAK,IAAI;;;;;;;;;;kCAwBxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,4JAAA,CAAA,UAAI;wCAAoB,MAAM,OAAO,IAAI;kDACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,6BAA6B,EAAE,OAAO,KAAK,EAAE;sEAC5D,cAAA,8OAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAqC,OAAO,KAAK;;;;;;8EAC/D,8OAAC;oEAAE,WAAU;8EAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCATvD,OAAO,KAAK;;;;;;;;;;;;;;;;kCAoB7B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,mOAAA,CAAA,qBAAkB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGjD,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACT,wBACC,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oDAAY,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;mDAFP;;;;;;;;;mDAMZ,YAAY,MAAM,GAAG,kBACvB,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;wDAA0B,WAAU;kEACnC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFAGjD,8OAAC;4EAAE,WAAU;;gFACV,KAAK,aAAa;gFAAC;;;;;;;;;;;;;8EAGxB,8OAAC;oEAAI,WAAU;8EACZ,KAAK,aAAa,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,aAAa,IAAI;;;;;;;;;;;;uDAXnD,KAAK,UAAU;;;;;8DAgB3B,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;kEAAS;;;;;;;;;;;;;;;;iEAM3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mOAAA,CAAA,qBAAkB;oDAAC,WAAU;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS9B,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAI5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;;;;;;;8DAI7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;sEACpD,8OAAC;4DAAE,WAAU;sEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D", "debugId": null}}]}