{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/animations.css"], "sourcesContent": ["/* ChatGPT-style animations */\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes typing {\n  0% {\n    width: 0;\n  }\n  100% {\n    width: 100%;\n  }\n}\n\n@keyframes blink {\n  0%, 50% {\n    opacity: 1;\n  }\n  51%, 100% {\n    opacity: 0;\n  }\n}\n\n/* Message animations */\n.message-enter {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n.typing-indicator {\n  animation: fadeIn 0.2s ease-out;\n}\n\n.loading-dots {\n  animation: pulse 1.5s ease-in-out infinite;\n}\n\n/* Button hover effects */\n.chat-button {\n  transition: all 0.2s ease-in-out;\n}\n\n.chat-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* Input focus effects */\n.chat-input {\n  transition: all 0.2s ease-in-out;\n}\n\n.chat-input:focus {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.2);\n}\n\n/* Avatar animations */\n.avatar {\n  transition: all 0.2s ease-in-out;\n}\n\n.avatar:hover {\n  transform: scale(1.05);\n}\n\n/* Image upload animations */\n.upload-area {\n  transition: all 0.3s ease-in-out;\n}\n\n.upload-area:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n/* Smooth scrolling */\n.messages-container {\n  scroll-behavior: smooth;\n}\n\n/* Loading spinner */\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.spinner {\n  animation: spin 1s linear infinite;\n}\n\n/* Gradient text effect */\n.gradient-text {\n  background: linear-gradient(135deg, #10b981, #3b82f6);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n/* Floating animation for icons */\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n.float-animation {\n  animation: float 3s ease-in-out infinite;\n}\n\n/* Shimmer effect for loading */\n@keyframes shimmer {\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n}\n\n.shimmer {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200px 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n/* Typewriter effect */\n.typewriter {\n  overflow: hidden;\n  border-right: 2px solid #10b981;\n  white-space: nowrap;\n  animation: typing 2s steps(40, end), blink 0.75s step-end infinite;\n}\n\n/* Slide in from right */\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n/* Slide in from left */\n@keyframes slideInLeft {\n  from {\n    transform: translateX(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n.slide-in-right {\n  animation: slideInRight 0.3s ease-out;\n}\n\n.slide-in-left {\n  animation: slideInLeft 0.3s ease-out;\n}\n\n/* Scale in animation */\n@keyframes scaleIn {\n  from {\n    transform: scale(0.8);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.scale-in {\n  animation: scaleIn 0.2s ease-out;\n}\n\n/* Bounce animation */\n@keyframes bounceIn {\n  0% {\n    transform: scale(0.3);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  70% {\n    transform: scale(0.9);\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.bounce-in {\n  animation: bounceIn 0.6s ease-out;\n}\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;;;;AAQA;;;;;;;;;;AASA;;;;AAKA;;;;;;;;;;AASA;;;;;AAOA;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAKA;;;;;;;;;;;;AAWA;;;;AAKA;;;;;;;;;;;;;;;;;;;;AAiBA"}}]}