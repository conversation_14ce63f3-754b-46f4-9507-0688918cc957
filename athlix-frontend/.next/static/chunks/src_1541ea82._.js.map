{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function formatTime(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function getInitials(firstName: string, lastName: string): string {\n  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // UUID v4 regex\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(sessionId);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,YAAY,SAAiB,EAAE,QAAgB;IAC7D,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;AAClE;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,iBAAiB,SAAiB;IAChD,gBAAgB;IAChB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';\n    \n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow',\n      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow',\n      outline: 'border border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground shadow-sm',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',\n      ghost: 'text-foreground hover:bg-accent hover:text-accent-foreground',\n      link: 'underline-offset-4 hover:underline text-primary',\n    };\n\n    const sizes = {\n      default: 'h-10 py-2 px-4',\n      sm: 'h-9 px-3 rounded-md',\n      lg: 'h-11 px-8 rounded-md',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/LoadingDots.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingDotsProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: string;\n}\n\nexport const LoadingDots: React.FC<LoadingDotsProps> = ({ \n  size = 'md', \n  color = 'bg-gray-500' \n}) => {\n  const sizeClasses = {\n    sm: 'w-1 h-1',\n    md: 'w-2 h-2',\n    lg: 'w-3 h-3'\n  };\n\n  return (\n    <div className=\"flex space-x-1 items-center\">\n      <div \n        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}\n        style={{ animationDelay: '0ms' }}\n      ></div>\n      <div \n        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}\n        style={{ animationDelay: '150ms' }}\n      ></div>\n      <div \n        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}\n        style={{ animationDelay: '300ms' }}\n      ></div>\n    </div>\n  );\n};\n\nexport const TypingIndicator: React.FC = () => {\n  return (\n    <div className=\"flex items-center space-x-2 p-4 bg-gray-50 rounded-lg max-w-xs\">\n      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n        <SparklesIcon className=\"w-4 h-4 text-white\" />\n      </div>\n      <div className=\"flex flex-col\">\n        <span className=\"text-xs text-gray-500 mb-1\">ATHLIX AI is typing...</span>\n        <LoadingDots size=\"sm\" color=\"bg-gray-400\" />\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingDots;\n"], "names": [], "mappings": ";;;;;;;AAOO,MAAM,cAA0C,CAAC,EACtD,OAAO,IAAI,EACX,QAAQ,aAAa,EACtB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,4BAA4B,CAAC;gBACtE,OAAO;oBAAE,gBAAgB;gBAAM;;;;;;0BAEjC,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,4BAA4B,CAAC;gBACtE,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAEnC,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,4BAA4B,CAAC;gBACtE,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;;;;;;;AAIzC;KA1Ba;AA4BN,MAAM,kBAA4B;IACvC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAa,WAAU;;;;;;;;;;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;kCAC7C,6LAAC;wBAAY,MAAK;wBAAK,OAAM;;;;;;;;;;;;;;;;;;AAIrC;MAZa;uCAcE", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/app/treatment/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Navbar } from '@/components/layout/Navbar';\nimport { Button } from '@/components/ui/Button';\nimport { Input } from '@/components/ui/Input';\nimport { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';\nimport {\n  ClipboardDocumentListIcon,\n  PhotoIcon,\n  CheckCircleIcon,\n  ArrowRightIcon,\n  CloudArrowUpIcon,\n  ChatBubbleLeftRightIcon,\n  PaperAirplaneIcon,\n  PlusIcon,\n  UserIcon,\n  SparklesIcon,\n  ClockIcon,\n  Bars3Icon\n} from '@heroicons/react/24/outline';\nimport { treatmentAPI, chatAPI } from '@/lib/api';\nimport toast from 'react-hot-toast';\nimport { LoadingDots, TypingIndicator } from '@/components/ui/LoadingDots';\n\ninterface TreatmentSession {\n  session_id: string;\n  status: 'image_upload' | 'conversation' | 'completed';\n  ai_response?: string;\n  conversation_history?: Array<{\n    role: 'user' | 'assistant';\n    content: string;\n  }>;\n  message?: string;\n}\n\ninterface ChatMessage {\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: string;\n}\n\nexport default function TreatmentPage() {\n  const { isAuthenticated } = useAuth();\n  const [treatmentSession, setTreatmentSession] = useState<TreatmentSession | null>(null);\n  const [currentAnswer, setCurrentAnswer] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState<'start' | 'image_upload' | 'conversation' | 'chat'>('start');\n  const [uploadedImage, setUploadedImage] = useState<string | null>(null);\n  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);\n  const [chatInput, setChatInput] = useState('');\n  const [chatSessionId, setChatSessionId] = useState<string | null>(null);\n  const [treatmentInput, setTreatmentInput] = useState('');\n  const [treatmentMessages, setTreatmentMessages] = useState<Array<{\n    role: 'user' | 'assistant';\n    content: string;\n    timestamp: string;\n  }>>([]);\n  const [chatHistory, setChatHistory] = useState<Array<{\n    session_id: string;\n    title: string;\n    last_activity: string;\n    type: 'treatment' | 'chat';\n  }>>([]);\n  const [showHistory, setShowHistory] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      window.location.href = '/login';\n      return;\n    }\n  }, [isAuthenticated]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [treatmentMessages, chatMessages, loading]);\n\n  // Load chat history on component mount\n  useEffect(() => {\n    loadChatHistory();\n  }, []);\n\n  const loadChatHistory = async () => {\n    try {\n      const [chatSessions, treatmentSessions] = await Promise.all([\n        chatAPI.getSessions(),\n        // We'll need to add a treatment sessions endpoint\n        fetch('http://localhost:8002/treatment/sessions', {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,\n          },\n        }).then(res => res.ok ? res.json() : [])\n      ]);\n\n      const history = [\n        ...chatSessions.map((session: any) => ({\n          session_id: session.session_id,\n          title: session.title || 'Chat Session',\n          last_activity: session.last_activity || session.created_at,\n          type: 'chat' as const\n        })),\n        ...treatmentSessions.map((session: any) => ({\n          session_id: session.session_id,\n          title: session.title || 'Treatment Session',\n          last_activity: session.last_activity || session.created_at,\n          type: 'treatment' as const\n        }))\n      ].sort((a, b) => new Date(b.last_activity).getTime() - new Date(a.last_activity).getTime());\n\n      setChatHistory(history);\n    } catch (error) {\n      console.error('Error loading chat history:', error);\n    }\n  };\n\n  const loadSession = async (sessionId: string, type: 'treatment' | 'chat') => {\n    try {\n      setLoading(true);\n\n      if (type === 'treatment') {\n        // Load treatment session\n        const response = await fetch(`http://localhost:8002/treatment/${sessionId}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,\n          },\n        });\n\n        if (response.ok) {\n          const sessionData = await response.json();\n          setTreatmentSession(sessionData);\n\n          // Load conversation history\n          if (sessionData.conversation_history) {\n            const messages = sessionData.conversation_history.map((msg: any) => ({\n              ...msg,\n              timestamp: msg.timestamp || new Date().toISOString()\n            }));\n            setTreatmentMessages(messages);\n          }\n\n          setStep('conversation');\n        }\n      } else {\n        // Load chat session\n        setChatSessionId(sessionId);\n\n        // Load chat history\n        const history = await chatAPI.getHistory(sessionId);\n        const messages = history.map((msg: any) => ({\n          role: msg.role,\n          content: msg.content,\n          timestamp: msg.timestamp\n        }));\n        setChatMessages(messages);\n\n        setStep('chat');\n      }\n\n      setShowHistory(false); // Close sidebar after loading\n      toast.success('Session loaded successfully');\n    } catch (error) {\n      console.error('Error loading session:', error);\n      toast.error('Failed to load session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startTreatmentSession = async () => {\n    setLoading(true);\n    try {\n      const response = await treatmentAPI.startSession();\n      setTreatmentSession(response);\n      setStep('image_upload');\n      toast.success('Treatment session started - please upload an image');\n    } catch (error) {\n      console.error('Error starting treatment session:', error);\n      toast.error('Failed to start treatment session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !treatmentSession) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      toast.error('Please upload an image file');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('Image size should be less than 5MB');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Convert to base64\n      const reader = new FileReader();\n      reader.onload = async (e) => {\n        const base64 = e.target?.result as string;\n        setUploadedImage(base64);\n\n        try {\n          const data = await treatmentAPI.uploadImage({\n            session_id: treatmentSession.session_id,\n            image_data: base64,\n          });\n\n          setTreatmentSession(data);\n\n          // Initialize conversation with AI response\n          if (data.conversation_history && data.conversation_history.length > 0) {\n            const messages = data.conversation_history.map((msg: any) => ({\n              ...msg,\n              timestamp: new Date().toISOString()\n            }));\n            setTreatmentMessages(messages);\n          } else if (data.ai_response) {\n            // Fallback: if no conversation_history but ai_response exists\n            const aiMessage = {\n              role: 'assistant' as const,\n              content: data.ai_response,\n              timestamp: new Date().toISOString()\n            };\n            setTreatmentMessages([aiMessage]);\n          }\n\n          setStep('conversation');\n          toast.success('Image uploaded and analyzed! You can now chat with our AI assistant.');\n        } catch (error) {\n          console.error('Error uploading image:', error);\n          toast.error('Failed to upload image');\n        } finally {\n          setLoading(false);\n        }\n      };\n      reader.readAsDataURL(file);\n    } catch (error) {\n      console.error('Error processing image:', error);\n      toast.error('Failed to process image');\n      setLoading(false);\n    }\n  };\n\n  const startDirectChat = async () => {\n    setLoading(true);\n    try {\n      const response = await chatAPI.createSession();\n      setChatSessionId(response.session_id);\n      setStep('chat');\n      toast.success('Chat session started');\n    } catch (error) {\n      console.error('Error starting chat session:', error);\n      toast.error('Failed to start chat session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendTreatmentMessage = async () => {\n    if (!treatmentSession || !treatmentInput.trim()) return;\n\n    const userMessage = {\n      role: 'user' as const,\n      content: treatmentInput,\n      timestamp: new Date().toISOString(),\n    };\n\n    setTreatmentMessages(prev => [...prev, userMessage]);\n    const currentInput = treatmentInput;\n    setTreatmentInput('');\n    setLoading(true);\n\n    try {\n      const response = await treatmentAPI.sendMessage({\n        session_id: treatmentSession.session_id,\n        message: currentInput,\n      });\n\n      const aiMessage = {\n        role: 'assistant' as const,\n        content: response.ai_response,\n        timestamp: new Date().toISOString(),\n      };\n\n      setTreatmentMessages(prev => [...prev, aiMessage]);\n\n      // Update session with latest conversation\n      setTreatmentSession(prev => prev ? {\n        ...prev,\n        conversation_history: response.conversation_history\n      } : null);\n\n    } catch (error) {\n      console.error('Error sending treatment message:', error);\n      toast.error('Failed to send message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendChatMessage = async () => {\n    if (!chatInput.trim() || !chatSessionId) return;\n\n    const userMessage: ChatMessage = {\n      role: 'user',\n      content: chatInput,\n      timestamp: new Date().toISOString(),\n    };\n\n    setChatMessages(prev => [...prev, userMessage]);\n    const currentInput = chatInput;\n    setChatInput('');\n    setLoading(true);\n\n    try {\n      const response = await chatAPI.sendMessage({\n        session_id: chatSessionId,\n        message: currentInput,\n      });\n\n      const aiMessage: ChatMessage = {\n        role: 'assistant',\n        content: response.response,\n        timestamp: response.timestamp,\n      };\n\n      setChatMessages(prev => [...prev, aiMessage]);\n    } catch (error) {\n      console.error('Error sending chat message:', error);\n      toast.error('Failed to send message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetSession = () => {\n    setTreatmentSession(null);\n    setCurrentAnswer('');\n    setUploadedImage(null);\n    setChatMessages([]);\n    setChatInput('');\n    setChatSessionId(null);\n    setTreatmentInput('');\n    setTreatmentMessages([]);\n    setStep('start');\n  };\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white flex\">\n      {/* Chat History Sidebar */}\n      {showHistory && (\n        <div className=\"w-80 border-r border-gray-200 bg-gray-50 flex flex-col\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Chat History</h2>\n          </div>\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-2\">\n            {chatHistory.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <ClockIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-500\">No chat history yet</p>\n              </div>\n            ) : (\n              chatHistory.map((session) => (\n                <button\n                  key={session.session_id}\n                  className=\"w-full text-left p-3 rounded-lg hover:bg-white transition-colors border border-transparent hover:border-gray-200\"\n                  onClick={() => loadSession(session.session_id, session.type)}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      session.type === 'treatment' ? 'bg-green-100' : 'bg-blue-100'\n                    }`}>\n                      {session.type === 'treatment' ? (\n                        <PhotoIcon className={`w-4 h-4 ${session.type === 'treatment' ? 'text-green-600' : 'text-blue-600'}`} />\n                      ) : (\n                        <ChatBubbleLeftRightIcon className={`w-4 h-4 ${session.type === 'treatment' ? 'text-green-600' : 'text-blue-600'}`} />\n                      )}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">\n                        {session.title}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {new Date(session.last_activity).toLocaleDateString()}\n                      </p>\n                    </div>\n                  </div>\n                </button>\n              ))\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* ChatGPT-style Header */}\n        <div className=\"border-b border-gray-200 bg-white sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-4 py-3 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n              <SparklesIcon className=\"w-5 h-5 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-semibold text-gray-900\">ATHLIX AI</h1>\n              <p className=\"text-xs text-gray-500\">Medical Assistant</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              onClick={() => setShowHistory(!showHistory)}\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"flex items-center space-x-2\"\n            >\n              <ClockIcon className=\"w-4 h-4\" />\n              <span>History</span>\n            </Button>\n            <Button\n              onClick={resetSession}\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"flex items-center space-x-2\"\n            >\n              <PlusIcon className=\"w-4 h-4\" />\n              <span>New Chat</span>\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Chat Area */}\n      <div className=\"flex-1 flex flex-col max-w-4xl mx-auto w-full\">{step === 'start' && (\n        <div className=\"flex-1 flex items-center justify-center p-8\">\n          <div className=\"text-center max-w-2xl\">\n            <div className=\"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <SparklesIcon className=\"w-8 h-8 text-white\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">\n              Welcome to ATHLIX AI\n            </h2>\n            <p className=\"text-gray-600 mb-8\">\n              Your AI medical assistant. Upload an image for analysis or start chatting directly.\n            </p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n              <button\n                onClick={startTreatmentSession}\n                disabled={loading}\n                className=\"p-6 border border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all group\"\n              >\n                <PhotoIcon className=\"w-8 h-8 text-green-600 mx-auto mb-3 group-hover:scale-110 transition-transform\" />\n                <h3 className=\"font-medium text-gray-900 mb-2\">Image Analysis</h3>\n                <p className=\"text-sm text-gray-600\">Upload a photo for AI-powered medical analysis</p>\n              </button>\n\n              <button\n                onClick={startDirectChat}\n                disabled={loading}\n                className=\"p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all group\"\n              >\n                <ChatBubbleLeftRightIcon className=\"w-8 h-8 text-blue-600 mx-auto mb-3 group-hover:scale-110 transition-transform\" />\n                <h3 className=\"font-medium text-gray-900 mb-2\">Direct Chat</h3>\n                <p className=\"text-sm text-gray-600\">Chat directly with our AI assistant</p>\n              </button>\n            </div>\n\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-left\">\n              <div className=\"flex\">\n                <svg className=\"h-5 w-5 text-yellow-400 mt-0.5 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                <div>\n                  <h3 className=\"text-sm font-medium text-yellow-800\">Medical Disclaimer</h3>\n                  <p className=\"mt-1 text-sm text-yellow-700\">\n                    This AI assistant provides information for educational purposes only and should not replace professional medical advice.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n        {step === 'image_upload' && treatmentSession && (\n          <div className=\"flex-1 flex items-center justify-center p-8\">\n            <div className=\"max-w-md w-full\">\n              <div className=\"text-center mb-8\">\n                <div className=\"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <PhotoIcon className=\"w-8 h-8 text-white\" />\n                </div>\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Upload Medical Image</h2>\n                <p className=\"text-gray-600\">Upload a clear image for AI analysis</p>\n              </div>\n\n              <div\n                className=\"border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors cursor-pointer upload-area\"\n                onClick={() => fileInputRef.current?.click()}\n              >\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageUpload}\n                  className=\"hidden\"\n                />\n                {loading ? (\n                  <div className=\"space-y-4\">\n                    <div className=\"w-12 h-12 mx-auto\">\n                      <LoadingDots size=\"lg\" color=\"bg-blue-500\" />\n                    </div>\n                    <p className=\"text-gray-600\">Analyzing image...</p>\n                  </div>\n                ) : uploadedImage ? (\n                  <div className=\"space-y-4\">\n                    <img\n                      src={uploadedImage}\n                      alt=\"Uploaded\"\n                      className=\"max-w-full max-h-48 mx-auto rounded-lg\"\n                    />\n                    <p className=\"text-green-600 font-medium\">✓ Image uploaded successfully!</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <CloudArrowUpIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div>\n                      <p className=\"text-lg font-medium text-gray-900\">Drop your image here</p>\n                      <p className=\"text-gray-500\">or click to browse</p>\n                      <p className=\"text-xs text-gray-400 mt-2\">PNG, JPG, GIF up to 5MB</p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {(step === 'conversation' || step === 'chat') && (\n          <>\n            {/* Chat Messages Area */}\n            <div className=\"flex-1 overflow-y-auto px-4 py-6\">\n              <div className=\"max-w-3xl mx-auto space-y-6\">\n                {/* Show uploaded image if in treatment conversation */}\n                {step === 'conversation' && uploadedImage && (\n                  <div className=\"flex justify-center\">\n                    <div className=\"bg-gray-50 rounded-xl p-4 max-w-sm\">\n                      <img\n                        src={uploadedImage}\n                        alt=\"Uploaded medical image\"\n                        className=\"w-full rounded-lg\"\n                      />\n                      <p className=\"text-xs text-gray-500 mt-2 text-center\">Uploaded for analysis</p>\n                    </div>\n                  </div>\n                )}\n\n\n\n                {/* Messages */}\n                {(step === 'conversation' ? treatmentMessages : chatMessages).length === 0 && !loading ? (\n                  <div className=\"text-center py-12\">\n                    <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <SparklesIcon className=\"w-8 h-8 text-gray-400\" />\n                    </div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                      {step === 'conversation' ? 'AI is analyzing your image...' : 'Start a conversation'}\n                    </h3>\n                    <p className=\"text-gray-500\">\n                      {step === 'conversation'\n                        ? 'Please wait while our AI analyzes your medical image'\n                        : 'Ask me anything about your health and wellness'\n                      }\n                    </p>\n                  </div>\n                ) : (\n                  <>\n                    {(step === 'conversation' ? treatmentMessages : chatMessages).map((message, index) => (\n                      <div key={index} className=\"flex items-start space-x-3 message-enter\">\n                        {/* Avatar */}\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 avatar ${\n                          message.role === 'user'\n                            ? 'bg-blue-500'\n                            : 'bg-green-500'\n                        }`}>\n                          {message.role === 'user' ? (\n                            <UserIcon className=\"w-5 h-5 text-white\" />\n                          ) : (\n                            <SparklesIcon className=\"w-5 h-5 text-white\" />\n                          )}\n                        </div>\n\n                        {/* Message Content */}\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <span className=\"text-sm font-medium text-gray-900\">\n                              {message.role === 'user' ? 'You' : 'ATHLIX AI'}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">\n                              {new Date(message.timestamp).toLocaleTimeString()}\n                            </span>\n                          </div>\n                          <div className=\"prose prose-sm max-w-none\">\n                            <p className=\"text-gray-800 whitespace-pre-wrap leading-relaxed\">\n                              {message.content}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n\n                    {/* Typing Indicator */}\n                    {loading && (\n                      <div className=\"flex items-start space-x-3 typing-indicator\">\n                        <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0\">\n                          <SparklesIcon className=\"w-5 h-5 text-white\" />\n                        </div>\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <span className=\"text-sm font-medium text-gray-900\">ATHLIX AI</span>\n                            <span className=\"text-xs text-gray-500\">typing...</span>\n                          </div>\n                          <div className=\"bg-gray-100 rounded-2xl px-4 py-3 inline-block\">\n                            <LoadingDots size=\"sm\" color=\"bg-gray-500\" />\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Scroll anchor */}\n                    <div ref={messagesEndRef} />\n                  </>\n                )}\n              </div>\n            </div>\n\n            {/* Input Area */}\n            <div className=\"border-t border-gray-200 bg-white p-4\">\n              <div className=\"max-w-3xl mx-auto\">\n                <div className=\"flex items-end space-x-3\">\n                  <div className=\"flex-1 relative\">\n                    <textarea\n                      value={step === 'conversation' ? treatmentInput : chatInput}\n                      onChange={(e) => {\n                        if (step === 'conversation') {\n                          setTreatmentInput(e.target.value);\n                        } else {\n                          setChatInput(e.target.value);\n                        }\n                      }}\n                      onKeyDown={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          if (step === 'conversation') {\n                            sendTreatmentMessage();\n                          } else {\n                            sendChatMessage();\n                          }\n                        }\n                      }}\n                      placeholder=\"Type your message...\"\n                      className=\"w-full resize-none border border-gray-300 rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent max-h-32 chat-input\"\n                      rows={1}\n                      style={{ minHeight: '48px' }}\n                    />\n                  </div>\n                  <button\n                    onClick={step === 'conversation' ? sendTreatmentMessage : sendChatMessage}\n                    disabled={\n                      loading ||\n                      (step === 'conversation' ? !treatmentInput.trim() : !chatInput.trim())\n                    }\n                    className=\"w-10 h-10 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-xl flex items-center justify-center transition-colors chat-button\"\n                  >\n                    {loading ? (\n                      <LoadingDots size=\"sm\" color=\"bg-white\" />\n                    ) : (\n                      <PaperAirplaneIcon className=\"w-5 h-5 text-white\" />\n                    )}\n                  </button>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-2 text-center\">\n                  Press Enter to send, Shift+Enter for new line\n                </p>\n              </div>\n            </div>\n          </>\n        )}\n\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;;;AAxBA;;;;;;;;AA2Ce,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IACrF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIrD,EAAE;IACN,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAKzC,EAAE;IACN,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF;QACF;kCAAG;QAAC;KAAgB;IAEpB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;kCAAG;QAAC;QAAmB;QAAc;KAAQ;IAE7C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,CAAC,cAAc,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,oHAAA,CAAA,UAAO,CAAC,WAAW;gBACnB,kDAAkD;gBAClD,MAAM,4CAA4C;oBAChD,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;oBACnE;gBACF,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,GAAG,IAAI,IAAI,KAAK,EAAE;aACxC;YAED,MAAM,UAAU;mBACX,aAAa,GAAG,CAAC,CAAC,UAAiB,CAAC;wBACrC,YAAY,QAAQ,UAAU;wBAC9B,OAAO,QAAQ,KAAK,IAAI;wBACxB,eAAe,QAAQ,aAAa,IAAI,QAAQ,UAAU;wBAC1D,MAAM;oBACR,CAAC;mBACE,kBAAkB,GAAG,CAAC,CAAC,UAAiB,CAAC;wBAC1C,YAAY,QAAQ,UAAU;wBAC9B,OAAO,QAAQ,KAAK,IAAI;wBACxB,eAAe,QAAQ,aAAa,IAAI,QAAQ,UAAU;wBAC1D,MAAM;oBACR,CAAC;aACF,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO;YAExF,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,cAAc,OAAO,WAAmB;QAC5C,IAAI;YACF,WAAW;YAEX,IAAI,SAAS,aAAa;gBACxB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,WAAW,EAAE;oBAC3E,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;oBACnE;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,cAAc,MAAM,SAAS,IAAI;oBACvC,oBAAoB;oBAEpB,4BAA4B;oBAC5B,IAAI,YAAY,oBAAoB,EAAE;wBACpC,MAAM,WAAW,YAAY,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;gCACnE,GAAG,GAAG;gCACN,WAAW,IAAI,SAAS,IAAI,IAAI,OAAO,WAAW;4BACpD,CAAC;wBACD,qBAAqB;oBACvB;oBAEA,QAAQ;gBACV;YACF,OAAO;gBACL,oBAAoB;gBACpB,iBAAiB;gBAEjB,oBAAoB;gBACpB,MAAM,UAAU,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;gBACzC,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC1C,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;wBACpB,WAAW,IAAI,SAAS;oBAC1B,CAAC;gBACD,gBAAgB;gBAEhB,QAAQ;YACV;YAEA,eAAe,QAAQ,8BAA8B;YACrD,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,YAAY;YAChD,oBAAoB;YACpB,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,kBAAkB;QAEhC,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QACX,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,OAAO;gBACrB,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,iBAAiB;gBAEjB,IAAI;oBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;wBAC1C,YAAY,iBAAiB,UAAU;wBACvC,YAAY;oBACd;oBAEA,oBAAoB;oBAEpB,2CAA2C;oBAC3C,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,CAAC,MAAM,GAAG,GAAG;wBACrE,MAAM,WAAW,KAAK,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;gCAC5D,GAAG,GAAG;gCACN,WAAW,IAAI,OAAO,WAAW;4BACnC,CAAC;wBACD,qBAAqB;oBACvB,OAAO,IAAI,KAAK,WAAW,EAAE;wBAC3B,8DAA8D;wBAC9D,MAAM,YAAY;4BAChB,MAAM;4BACN,SAAS,KAAK,WAAW;4BACzB,WAAW,IAAI,OAAO,WAAW;wBACnC;wBACA,qBAAqB;4BAAC;yBAAU;oBAClC;oBAEA,QAAQ;oBACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAChB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACd,SAAU;oBACR,WAAW;gBACb;YACF;YACA,OAAO,aAAa,CAAC;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa;YAC5C,iBAAiB,SAAS,UAAU;YACpC,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,oBAAoB,CAAC,eAAe,IAAI,IAAI;QAEjD,MAAM,cAAc;YAClB,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,qBAAqB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QACnD,MAAM,eAAe;QACrB,kBAAkB;QAClB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBAC9C,YAAY,iBAAiB,UAAU;gBACvC,SAAS;YACX;YAEA,MAAM,YAAY;gBAChB,MAAM;gBACN,SAAS,SAAS,WAAW;gBAC7B,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,qBAAqB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YAEjD,0CAA0C;YAC1C,oBAAoB,CAAA,OAAQ,OAAO;oBACjC,GAAG,IAAI;oBACP,sBAAsB,SAAS,oBAAoB;gBACrD,IAAI;QAEN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,eAAe;QAEzC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC9C,MAAM,eAAe;QACrB,aAAa;QACb,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBACzC,YAAY;gBACZ,SAAS;YACX;YAEA,MAAM,YAAyB;gBAC7B,MAAM;gBACN,SAAS,SAAS,QAAQ;gBAC1B,WAAW,SAAS,SAAS;YAC/B;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,oBAAoB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB,EAAE;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB,EAAE;QACvB,QAAQ;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;kCACZ,YAAY,MAAM,KAAK,kBACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;mCAG/B,YAAY,GAAG,CAAC,CAAC,wBACf,6LAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,YAAY,QAAQ,UAAU,EAAE,QAAQ,IAAI;0CAE3D,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,IAAI,KAAK,cAAc,iBAAiB,eAChD;sDACC,QAAQ,IAAI,KAAK,4BAChB,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,IAAI,KAAK,cAAc,mBAAmB,iBAAiB;;;;;qEAEpG,6LAAC,gPAAA,CAAA,0BAAuB;gDAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,IAAI,KAAK,cAAc,mBAAmB,iBAAiB;;;;;;;;;;;sDAGtH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DACV,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;+BAnBpD,QAAQ,UAAU;;;;;;;;;;;;;;;;0BA+BnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,eAAe,CAAC;4CAC/B,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAI,WAAU;;4BAAiD,SAAS,yBACvE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAIlC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAGvC,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,6LAAC,gPAAA,CAAA,0BAAuB;4DAAC,WAAU;;;;;;sEACnC,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAsC,SAAQ;wDAAY,MAAK;kEAC5E,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAoN,UAAS;;;;;;;;;;;kEAE1P,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUrD,SAAS,kBAAkB,kCAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAG/B,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,aAAa,OAAO,EAAE;;8DAErC,6LAAC;oDACC,KAAK;oDACL,MAAK;oDACL,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;gDAEX,wBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,0IAAA,CAAA,cAAW;gEAAC,MAAK;gEAAK,OAAM;;;;;;;;;;;sEAE/B,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;2DAE7B,8BACF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KAAK;4DACL,KAAI;4DACJ,WAAU;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;yEAG5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,kOAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;sEAC5B,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASvD,CAAC,SAAS,kBAAkB,SAAS,MAAM,mBAC1C;;kDAEE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;gDAEZ,SAAS,kBAAkB,+BAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,KAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;0EAEZ,6LAAC;gEAAE,WAAU;0EAAyC;;;;;;;;;;;;;;;;;gDAQ3D,CAAC,SAAS,iBAAiB,oBAAoB,YAAY,EAAE,MAAM,KAAK,KAAK,CAAC,wBAC7E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,6LAAC;4DAAG,WAAU;sEACX,SAAS,iBAAiB,kCAAkC;;;;;;sEAE/D,6LAAC;4DAAE,WAAU;sEACV,SAAS,iBACN,yDACA;;;;;;;;;;;yEAKR;;wDACG,CAAC,SAAS,iBAAiB,oBAAoB,YAAY,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC1E,6LAAC;gEAAgB,WAAU;;kFAEzB,6LAAC;wEAAI,WAAW,CAAC,2EAA2E,EAC1F,QAAQ,IAAI,KAAK,SACb,gBACA,gBACJ;kFACC,QAAQ,IAAI,KAAK,uBAChB,6LAAC,kNAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;iGAEpB,6LAAC,0NAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;kFAK5B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGACb,QAAQ,IAAI,KAAK,SAAS,QAAQ;;;;;;kGAErC,6LAAC;wFAAK,WAAU;kGACb,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0FAGnD,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFAAE,WAAU;8FACV,QAAQ,OAAO;;;;;;;;;;;;;;;;;;+DA1Bd;;;;;wDAkCX,yBACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;8EAE1B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAoC;;;;;;8FACpD,6LAAC;oFAAK,WAAU;8FAAwB;;;;;;;;;;;;sFAE1C,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,0IAAA,CAAA,cAAW;gFAAC,MAAK;gFAAK,OAAM;;;;;;;;;;;;;;;;;;;;;;;sEAOrC,6LAAC;4DAAI,KAAK;;;;;;;;;;;;;;;;;;;kDAOlB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,OAAO,SAAS,iBAAiB,iBAAiB;gEAClD,UAAU,CAAC;oEACT,IAAI,SAAS,gBAAgB;wEAC3B,kBAAkB,EAAE,MAAM,CAAC,KAAK;oEAClC,OAAO;wEACL,aAAa,EAAE,MAAM,CAAC,KAAK;oEAC7B;gEACF;gEACA,WAAW,CAAC;oEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wEACpC,EAAE,cAAc;wEAChB,IAAI,SAAS,gBAAgB;4EAC3B;wEACF,OAAO;4EACL;wEACF;oEACF;gEACF;gEACA,aAAY;gEACZ,WAAU;gEACV,MAAM;gEACN,OAAO;oEAAE,WAAW;gEAAO;;;;;;;;;;;sEAG/B,6LAAC;4DACC,SAAS,SAAS,iBAAiB,uBAAuB;4DAC1D,UACE,WACA,CAAC,SAAS,iBAAiB,CAAC,eAAe,IAAI,KAAK,CAAC,UAAU,IAAI,EAAE;4DAEvE,WAAU;sEAET,wBACC,6LAAC,0IAAA,CAAA,cAAW;gEAAC,MAAK;gEAAK,OAAM;;;;;qFAE7B,6LAAC,oOAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAInC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYtE;GAvpBwB;;QACM,kIAAA,CAAA,UAAO;;;KADb", "debugId": null}}]}