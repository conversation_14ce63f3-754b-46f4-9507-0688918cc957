/* [project]/src/styles/animations.css [app-client] (css) */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

@keyframes typing {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }

  51%, 100% {
    opacity: 0;
  }
}

.message-enter {
  animation: .3s ease-out fadeInUp;
}

.typing-indicator {
  animation: .2s ease-out fadeIn;
}

.loading-dots {
  animation: 1.5s ease-in-out infinite pulse;
}

.chat-button {
  transition: all .2s ease-in-out;
}

.chat-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #0000001a;
}

.chat-input {
  transition: all .2s ease-in-out;
}

.chat-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px #22c55e33;
}

.avatar {
  transition: all .2s ease-in-out;
}

.avatar:hover {
  transform: scale(1.05);
}

.upload-area {
  transition: all .3s ease-in-out;
}

.upload-area:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #0000001a;
}

.messages-container {
  scroll-behavior: smooth;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: 1s linear infinite spin;
}

.gradient-text {
  background: linear-gradient(135deg, #10b981, #3b82f6);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: 3s ease-in-out infinite float;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200px 100%;
  animation: 1.5s infinite shimmer;
}

.typewriter {
  white-space: nowrap;
  border-right: 2px solid #10b981;
  animation: 2s steps(40, end) typing, .75s step-end infinite blink;
  overflow: hidden;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: .3s ease-out slideInRight;
}

.slide-in-left {
  animation: .3s ease-out slideInLeft;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.scale-in {
  animation: .2s ease-out scaleIn;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.bounce-in {
  animation: .6s ease-out bounceIn;
}

/*# sourceMappingURL=src_styles_animations_css_f9ee138c._.single.css.map*/