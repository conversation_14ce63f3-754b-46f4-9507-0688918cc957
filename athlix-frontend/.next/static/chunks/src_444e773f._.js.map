{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use((config) => {\n  if (typeof window !== 'undefined') {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n  }\n  return config;\n});\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Types\nexport interface User {\n  id: string;\n  first_name: string;\n  last_name: string;\n  email: string;\n  created_at: string;\n}\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  first_name: string;\n  last_name: string;\n  email: string;\n  password: string;\n}\n\nexport interface ChatMessage {\n  session_id?: string;\n  message: string;\n  message_type?: string;\n}\n\nexport interface ChatResponse {\n  session_id: string;\n  response: string;\n  timestamp: string;\n}\n\nexport interface ChatSession {\n  session_id: string;\n  message_count: number;\n  last_activity: string | null;\n}\n\nexport interface TreatmentQuestion {\n  session_id: string;\n  question_id: string;\n  answer: string;\n}\n\n// Auth API\nexport const authAPI = {\n  register: async (data: RegisterRequest) => {\n    const response = await api.post('/signup', data);\n    return response.data;\n  },\n\n  login: async (data: LoginRequest) => {\n    // Convert to form data for OAuth2PasswordRequestForm\n    const formData = new FormData();\n    formData.append('username', data.email);\n    formData.append('password', data.password);\n\n    const response = await api.post('/signin', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n    return response.data;\n  },\n\n  getUsers: async () => {\n    const response = await api.get('/users');\n    return response.data;\n  },\n};\n\n// Chat API\nexport const chatAPI = {\n  createSession: async () => {\n    const response = await api.post('/chat/new');\n    return response.data;\n  },\n\n  sendMessage: async (data: ChatMessage) => {\n    const response = await api.post('/chat/message', data);\n    return response.data;\n  },\n\n  getSessions: async () => {\n    const response = await api.get('/chat/sessions');\n    return response.data;\n  },\n\n  getChatHistory: async (sessionId: string) => {\n    const response = await api.get(`/chat/${sessionId}/history`);\n    return response.data;\n  },\n};\n\n// Treatment API\nexport const treatmentAPI = {\n  startSession: async () => {\n    const response = await api.post('/treatment/start');\n    return response.data;\n  },\n\n  uploadImage: async (data: { image_data: string; session_id?: string }) => {\n    const response = await api.post('/treatment/upload-image', data);\n    return response.data;\n  },\n\n  answerQuestion: async (data: TreatmentQuestion) => {\n    const response = await api.post('/treatment/answer', data);\n    return response.data;\n  },\n\n  getSession: async (sessionId: string) => {\n    const response = await api.get(`/treatment/${sessionId}`);\n    return response.data;\n  },\n};\n\n// System API\nexport const systemAPI = {\n  getHealth: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n\n  getInfo: async () => {\n    const response = await api.get('/');\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;IACF;IACA,OAAO;AACT;AAEA,6CAA6C;AAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,aAAkB,aAAa;QACnE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAiDK,MAAM,UAAU;IACrB,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OAAO;QACZ,qDAAqD;QACrD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,KAAK,KAAK;QACtC,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;QAEzC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW,UAAU;YACnD,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,eAAe;QACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,iBAAiB;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,QAAQ,CAAC;QAC3D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,cAAc;QACZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;QACxD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,WAAW;QACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;QACP,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, authAPI, LoginRequest, RegisterRequest } from '@/lib/api';\nimport toast from 'react-hot-toast';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  login: (data: LoginRequest) => Promise<boolean>;\n  register: (data: RegisterRequest) => Promise<boolean>;\n  logout: () => void;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in on app start\n    // Only run on client side to avoid hydration mismatch\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('access_token');\n      const userData = localStorage.getItem('user');\n\n      if (token && userData) {\n        try {\n          setUser(JSON.parse(userData));\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          localStorage.removeItem('access_token');\n          localStorage.removeItem('user');\n        }\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (data: LoginRequest): Promise<boolean> => {\n    try {\n      setLoading(true);\n      const response = await authAPI.login(data);\n\n      if (response.access_token && response.user_id) {\n        // Create a user object from the login data since backend doesn't return full user details\n        const userData: User = {\n          id: response.user_id.toString(),\n          first_name: data.email.split('@')[0], // Temporary - use email prefix as first name\n          last_name: '',\n          email: data.email,\n          created_at: new Date().toISOString(),\n        };\n\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('access_token', response.access_token);\n          localStorage.setItem('refresh_token', response.refresh_token);\n          localStorage.setItem('user', JSON.stringify(userData));\n        }\n        setUser(userData);\n        toast.success('Login successful!');\n        return true;\n      }\n\n      toast.error('Login failed');\n      return false;\n    } catch (error: any) {\n      console.error('Login error:', error);\n      toast.error(error.response?.data?.detail || 'Login failed');\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterRequest): Promise<boolean> => {\n    try {\n      setLoading(true);\n      const response = await authAPI.register(data);\n\n      if (response.message && response.message.includes('successfully')) {\n        toast.success('Registration successful! Please login.');\n        return true;\n      }\n\n      toast.error('Registration failed');\n      return false;\n    } catch (error: any) {\n      console.error('Registration error:', error);\n      toast.error(error.response?.data?.detail || 'Registration failed');\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('access_token');\n      localStorage.removeItem('refresh_token');\n      localStorage.removeItem('user');\n    }\n    setUser(null);\n    toast.success('Logged out successfully');\n  };\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    isAuthenticated: !!user,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,0CAA0C;YAC1C,sDAAsD;YACtD,wCAAmC;gBACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,MAAM,WAAW,aAAa,OAAO,CAAC;gBAEtC,IAAI,SAAS,UAAU;oBACrB,IAAI;wBACF,QAAQ,KAAK,KAAK,CAAC;oBACrB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,aAAa,UAAU,CAAC;wBACxB,aAAa,UAAU,CAAC;oBAC1B;gBACF;YACF;YAEA,WAAW;QACb;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YAErC,IAAI,SAAS,YAAY,IAAI,SAAS,OAAO,EAAE;gBAC7C,0FAA0F;gBAC1F,MAAM,WAAiB;oBACrB,IAAI,SAAS,OAAO,CAAC,QAAQ;oBAC7B,YAAY,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACpC,WAAW;oBACX,OAAO,KAAK,KAAK;oBACjB,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,wCAAmC;oBACjC,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,aAAa,OAAO,CAAC,iBAAiB,SAAS,aAAa;oBAC5D,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC9C;gBACA,QAAQ;gBACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT;YAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;YAC5C,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;YAExC,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,QAAQ,CAAC,iBAAiB;gBACjE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT;YAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;YAC5C,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;QACA,QAAQ;QACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA1GgB;KAAA;AA4GT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}