{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function formatTime(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function getInitials(firstName: string, lastName: string): string {\n  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // UUID v4 regex\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(sessionId);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,YAAY,SAAiB,EAAE,QAAgB;IAC7D,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;AAClE;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,iBAAiB,SAAiB;IAChD,gBAAgB;IAChB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';\n    \n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow',\n      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow',\n      outline: 'border border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground shadow-sm',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',\n      ghost: 'text-foreground hover:bg-accent hover:text-accent-foreground',\n      link: 'underline-offset-4 hover:underline text-primary',\n    };\n\n    const sizes = {\n      default: 'h-10 py-2 px-4',\n      sm: 'h-9 px-3 rounded-md',\n      lg: 'h-11 px-8 rounded-md',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium leading-none text-foreground peer-disabled:cursor-not-allowed peer-disabled:opacity-70\">\n            {label}\n          </label>\n        )}\n        <input\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-md border border-input bg-background text-foreground px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-destructive focus-visible:ring-destructive',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"text-sm text-muted-foreground\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gXACA,SAAS,qDACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;YAE1C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border border-border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight text-card-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/LoadingDots.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingDotsProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: string;\n}\n\nexport const LoadingDots: React.FC<LoadingDotsProps> = ({ \n  size = 'md', \n  color = 'bg-gray-500' \n}) => {\n  const sizeClasses = {\n    sm: 'w-1 h-1',\n    md: 'w-2 h-2',\n    lg: 'w-3 h-3'\n  };\n\n  return (\n    <div className=\"flex space-x-1 items-center\">\n      <div \n        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}\n        style={{ animationDelay: '0ms' }}\n      ></div>\n      <div \n        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}\n        style={{ animationDelay: '150ms' }}\n      ></div>\n      <div \n        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}\n        style={{ animationDelay: '300ms' }}\n      ></div>\n    </div>\n  );\n};\n\nexport const TypingIndicator: React.FC = () => {\n  return (\n    <div className=\"flex items-center space-x-2 p-4 bg-gray-50 rounded-lg max-w-xs\">\n      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n        <SparklesIcon className=\"w-4 h-4 text-white\" />\n      </div>\n      <div className=\"flex flex-col\">\n        <span className=\"text-xs text-gray-500 mb-1\">ATHLIX AI is typing...</span>\n        <LoadingDots size=\"sm\" color=\"bg-gray-400\" />\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingDots;\n"], "names": [], "mappings": ";;;;;;;AAOO,MAAM,cAA0C,CAAC,EACtD,OAAO,IAAI,EACX,QAAQ,aAAa,EACtB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,4BAA4B,CAAC;gBACtE,OAAO;oBAAE,gBAAgB;gBAAM;;;;;;0BAEjC,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,4BAA4B,CAAC;gBACtE,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAEnC,6LAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,4BAA4B,CAAC;gBACtE,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;;;;;;;AAIzC;KA1Ba;AA4BN,MAAM,kBAA4B;IACvC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAa,WAAU;;;;;;;;;;;0BAE1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;kCAC7C,6LAAC;wBAAY,MAAK;wBAAK,OAAM;;;;;;;;;;;;;;;;;;AAIrC;MAZa;uCAcE", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/app/treatment/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Navbar } from '@/components/layout/Navbar';\nimport { Button } from '@/components/ui/Button';\nimport { Input } from '@/components/ui/Input';\nimport { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';\nimport {\n  ClipboardDocumentListIcon,\n  PhotoIcon,\n  CheckCircleIcon,\n  ArrowRightIcon,\n  CloudArrowUpIcon,\n  ChatBubbleLeftRightIcon,\n  PaperAirplaneIcon,\n  PlusIcon,\n  UserIcon,\n  SparklesIcon\n} from '@heroicons/react/24/outline';\nimport { treatmentAPI, chatAPI } from '@/lib/api';\nimport toast from 'react-hot-toast';\nimport { LoadingDots, TypingIndicator } from '@/components/ui/LoadingDots';\n\ninterface TreatmentSession {\n  session_id: string;\n  status: 'image_upload' | 'conversation' | 'completed';\n  ai_response?: string;\n  conversation_history?: Array<{\n    role: 'user' | 'assistant';\n    content: string;\n  }>;\n  message?: string;\n}\n\ninterface ChatMessage {\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: string;\n}\n\nexport default function TreatmentPage() {\n  const { isAuthenticated } = useAuth();\n  const [treatmentSession, setTreatmentSession] = useState<TreatmentSession | null>(null);\n  const [currentAnswer, setCurrentAnswer] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState<'start' | 'image_upload' | 'conversation' | 'chat'>('start');\n  const [uploadedImage, setUploadedImage] = useState<string | null>(null);\n  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);\n  const [chatInput, setChatInput] = useState('');\n  const [chatSessionId, setChatSessionId] = useState<string | null>(null);\n  const [treatmentInput, setTreatmentInput] = useState('');\n  const [treatmentMessages, setTreatmentMessages] = useState<Array<{\n    role: 'user' | 'assistant';\n    content: string;\n    timestamp: string;\n  }>>([]);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      window.location.href = '/login';\n      return;\n    }\n  }, [isAuthenticated]);\n\n  const startTreatmentSession = async () => {\n    setLoading(true);\n    try {\n      const response = await treatmentAPI.startSession();\n      setTreatmentSession(response);\n      setStep('image_upload');\n      toast.success('Treatment session started - please upload an image');\n    } catch (error) {\n      console.error('Error starting treatment session:', error);\n      toast.error('Failed to start treatment session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !treatmentSession) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      toast.error('Please upload an image file');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('Image size should be less than 5MB');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Convert to base64\n      const reader = new FileReader();\n      reader.onload = async (e) => {\n        const base64 = e.target?.result as string;\n        setUploadedImage(base64);\n\n        try {\n          const data = await treatmentAPI.uploadImage({\n            session_id: treatmentSession.session_id,\n            image_data: base64,\n          });\n\n          setTreatmentSession(data);\n\n          // Initialize conversation with AI response\n          if (data.conversation_history) {\n            const messages = data.conversation_history.map((msg: any) => ({\n              ...msg,\n              timestamp: new Date().toISOString()\n            }));\n            setTreatmentMessages(messages);\n          }\n\n          setStep('conversation');\n          toast.success('Image uploaded and analyzed! You can now chat with our AI assistant.');\n        } catch (error) {\n          console.error('Error uploading image:', error);\n          toast.error('Failed to upload image');\n        } finally {\n          setLoading(false);\n        }\n      };\n      reader.readAsDataURL(file);\n    } catch (error) {\n      console.error('Error processing image:', error);\n      toast.error('Failed to process image');\n      setLoading(false);\n    }\n  };\n\n  const startDirectChat = async () => {\n    setLoading(true);\n    try {\n      const response = await chatAPI.createSession();\n      setChatSessionId(response.session_id);\n      setStep('chat');\n      toast.success('Chat session started');\n    } catch (error) {\n      console.error('Error starting chat session:', error);\n      toast.error('Failed to start chat session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendTreatmentMessage = async () => {\n    if (!treatmentSession || !treatmentInput.trim()) return;\n\n    const userMessage = {\n      role: 'user' as const,\n      content: treatmentInput,\n      timestamp: new Date().toISOString(),\n    };\n\n    setTreatmentMessages(prev => [...prev, userMessage]);\n    const currentInput = treatmentInput;\n    setTreatmentInput('');\n    setLoading(true);\n\n    try {\n      const response = await treatmentAPI.sendMessage({\n        session_id: treatmentSession.session_id,\n        message: currentInput,\n      });\n\n      const aiMessage = {\n        role: 'assistant' as const,\n        content: response.ai_response,\n        timestamp: new Date().toISOString(),\n      };\n\n      setTreatmentMessages(prev => [...prev, aiMessage]);\n\n      // Update session with latest conversation\n      setTreatmentSession(prev => prev ? {\n        ...prev,\n        conversation_history: response.conversation_history\n      } : null);\n\n    } catch (error) {\n      console.error('Error sending treatment message:', error);\n      toast.error('Failed to send message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendChatMessage = async () => {\n    if (!chatInput.trim() || !chatSessionId) return;\n\n    const userMessage: ChatMessage = {\n      role: 'user',\n      content: chatInput,\n      timestamp: new Date().toISOString(),\n    };\n\n    setChatMessages(prev => [...prev, userMessage]);\n    const currentInput = chatInput;\n    setChatInput('');\n    setLoading(true);\n\n    try {\n      const response = await chatAPI.sendMessage({\n        session_id: chatSessionId,\n        message: currentInput,\n      });\n\n      const aiMessage: ChatMessage = {\n        role: 'assistant',\n        content: response.response,\n        timestamp: response.timestamp,\n      };\n\n      setChatMessages(prev => [...prev, aiMessage]);\n    } catch (error) {\n      console.error('Error sending chat message:', error);\n      toast.error('Failed to send message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetSession = () => {\n    setTreatmentSession(null);\n    setCurrentAnswer('');\n    setUploadedImage(null);\n    setChatMessages([]);\n    setChatInput('');\n    setChatSessionId(null);\n    setTreatmentInput('');\n    setTreatmentMessages([]);\n    setStep('start');\n  };\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white flex flex-col\">\n      {/* ChatGPT-style Header */}\n      <div className=\"border-b border-gray-200 bg-white sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-4 py-3 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n              <SparklesIcon className=\"w-5 h-5 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-semibold text-gray-900\">ATHLIX AI</h1>\n              <p className=\"text-xs text-gray-500\">Medical Assistant</p>\n            </div>\n          </div>\n          <Button\n            onClick={resetSession}\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"flex items-center space-x-2\"\n          >\n            <PlusIcon className=\"w-4 h-4\" />\n            <span>New Chat</span>\n          </Button>\n        </div>\n      </div>\n\n      {/* Main Chat Area */}\n      <div className=\"flex-1 flex flex-col max-w-4xl mx-auto w-full\">{step === 'start' && (\n        <div className=\"flex-1 flex items-center justify-center p-8\">\n          <div className=\"text-center max-w-2xl\">\n            <div className=\"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <SparklesIcon className=\"w-8 h-8 text-white\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">\n              Welcome to ATHLIX AI\n            </h2>\n            <p className=\"text-gray-600 mb-8\">\n              Your AI medical assistant. Upload an image for analysis or start chatting directly.\n            </p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n              <button\n                onClick={startTreatmentSession}\n                disabled={loading}\n                className=\"p-6 border border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all group\"\n              >\n                <PhotoIcon className=\"w-8 h-8 text-green-600 mx-auto mb-3 group-hover:scale-110 transition-transform\" />\n                <h3 className=\"font-medium text-gray-900 mb-2\">Image Analysis</h3>\n                <p className=\"text-sm text-gray-600\">Upload a photo for AI-powered medical analysis</p>\n              </button>\n\n              <button\n                onClick={startDirectChat}\n                disabled={loading}\n                className=\"p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all group\"\n              >\n                <ChatBubbleLeftRightIcon className=\"w-8 h-8 text-blue-600 mx-auto mb-3 group-hover:scale-110 transition-transform\" />\n                <h3 className=\"font-medium text-gray-900 mb-2\">Direct Chat</h3>\n                <p className=\"text-sm text-gray-600\">Chat directly with our AI assistant</p>\n              </button>\n            </div>\n\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-left\">\n              <div className=\"flex\">\n                <svg className=\"h-5 w-5 text-yellow-400 mt-0.5 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                <div>\n                  <h3 className=\"text-sm font-medium text-yellow-800\">Medical Disclaimer</h3>\n                  <p className=\"mt-1 text-sm text-yellow-700\">\n                    This AI assistant provides information for educational purposes only and should not replace professional medical advice.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n        {step === 'image_upload' && treatmentSession && (\n          <div className=\"flex-1 flex items-center justify-center p-8\">\n            <div className=\"max-w-md w-full\">\n              <div className=\"text-center mb-8\">\n                <div className=\"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <PhotoIcon className=\"w-8 h-8 text-white\" />\n                </div>\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Upload Medical Image</h2>\n                <p className=\"text-gray-600\">Upload a clear image for AI analysis</p>\n              </div>\n\n              <div\n                className=\"border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors cursor-pointer\"\n                onClick={() => fileInputRef.current?.click()}\n              >\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageUpload}\n                  className=\"hidden\"\n                />\n                {loading ? (\n                  <div className=\"space-y-4\">\n                    <div className=\"w-12 h-12 mx-auto\">\n                      <LoadingDots size=\"lg\" color=\"bg-blue-500\" />\n                    </div>\n                    <p className=\"text-gray-600\">Analyzing image...</p>\n                  </div>\n                ) : uploadedImage ? (\n                  <div className=\"space-y-4\">\n                    <img\n                      src={uploadedImage}\n                      alt=\"Uploaded\"\n                      className=\"max-w-full max-h-48 mx-auto rounded-lg\"\n                    />\n                    <p className=\"text-green-600 font-medium\">✓ Image uploaded successfully!</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <CloudArrowUpIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div>\n                      <p className=\"text-lg font-medium text-gray-900\">Drop your image here</p>\n                      <p className=\"text-gray-500\">or click to browse</p>\n                      <p className=\"text-xs text-gray-400 mt-2\">PNG, JPG, GIF up to 5MB</p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {(step === 'conversation' || step === 'chat') && (\n          <>\n            {/* Chat Messages Area */}\n            <div className=\"flex-1 overflow-y-auto px-4 py-6\">\n              <div className=\"max-w-3xl mx-auto space-y-6\">\n                {/* Show uploaded image if in treatment conversation */}\n                {step === 'conversation' && uploadedImage && (\n                  <div className=\"flex justify-center\">\n                    <div className=\"bg-gray-50 rounded-xl p-4 max-w-sm\">\n                      <img\n                        src={uploadedImage}\n                        alt=\"Uploaded medical image\"\n                        className=\"w-full rounded-lg\"\n                      />\n                      <p className=\"text-xs text-gray-500 mt-2 text-center\">Uploaded for analysis</p>\n                    </div>\n                  </div>\n                )}\n\n                {/* Messages */}\n                {(step === 'conversation' ? treatmentMessages : chatMessages).length === 0 && !loading ? (\n                  <div className=\"text-center py-12\">\n                    <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <SparklesIcon className=\"w-8 h-8 text-gray-400\" />\n                    </div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                      {step === 'conversation' ? 'AI is analyzing your image...' : 'Start a conversation'}\n                    </h3>\n                    <p className=\"text-gray-500\">\n                      {step === 'conversation'\n                        ? 'Please wait while our AI analyzes your medical image'\n                        : 'Ask me anything about your health and wellness'\n                      }\n                    </p>\n                  </div>\n                ) : (\n                  <>\n                    {(step === 'conversation' ? treatmentMessages : chatMessages).map((message, index) => (\n                      <div key={index} className=\"flex items-start space-x-3\">\n                        {/* Avatar */}\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${\n                          message.role === 'user'\n                            ? 'bg-blue-500'\n                            : 'bg-green-500'\n                        }`}>\n                          {message.role === 'user' ? (\n                            <UserIcon className=\"w-5 h-5 text-white\" />\n                          ) : (\n                            <SparklesIcon className=\"w-5 h-5 text-white\" />\n                          )}\n                        </div>\n\n                        {/* Message Content */}\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <span className=\"text-sm font-medium text-gray-900\">\n                              {message.role === 'user' ? 'You' : 'ATHLIX AI'}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">\n                              {new Date(message.timestamp).toLocaleTimeString()}\n                            </span>\n                          </div>\n                          <div className=\"prose prose-sm max-w-none\">\n                            <p className=\"text-gray-800 whitespace-pre-wrap leading-relaxed\">\n                              {message.content}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n\n                    {/* Typing Indicator */}\n                    {loading && (\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0\">\n                          <SparklesIcon className=\"w-5 h-5 text-white\" />\n                        </div>\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <span className=\"text-sm font-medium text-gray-900\">ATHLIX AI</span>\n                            <span className=\"text-xs text-gray-500\">typing...</span>\n                          </div>\n                          <div className=\"bg-gray-100 rounded-2xl px-4 py-3 inline-block\">\n                            <LoadingDots size=\"sm\" color=\"bg-gray-500\" />\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </>\n                )}\n              </div>\n            </div>\n\n            {/* Input Area */}\n            <div className=\"border-t border-gray-200 bg-white p-4\">\n              <div className=\"max-w-3xl mx-auto\">\n                <div className=\"flex items-end space-x-3\">\n                  <div className=\"flex-1 relative\">\n                    <textarea\n                      value={step === 'conversation' ? treatmentInput : chatInput}\n                      onChange={(e) => {\n                        if (step === 'conversation') {\n                          setTreatmentInput(e.target.value);\n                        } else {\n                          setChatInput(e.target.value);\n                        }\n                      }}\n                      onKeyDown={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          if (step === 'conversation') {\n                            sendTreatmentMessage();\n                          } else {\n                            sendChatMessage();\n                          }\n                        }\n                      }}\n                      placeholder=\"Type your message...\"\n                      className=\"w-full resize-none border border-gray-300 rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent max-h-32\"\n                      rows={1}\n                      style={{ minHeight: '48px' }}\n                    />\n                  </div>\n                  <button\n                    onClick={step === 'conversation' ? sendTreatmentMessage : sendChatMessage}\n                    disabled={\n                      loading ||\n                      (step === 'conversation' ? !treatmentInput.trim() : !chatInput.trim())\n                    }\n                    className=\"w-10 h-10 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-xl flex items-center justify-center transition-colors\"\n                  >\n                    {loading ? (\n                      <LoadingDots size=\"sm\" color=\"bg-white\" />\n                    ) : (\n                      <PaperAirplaneIcon className=\"w-5 h-5 text-white\" />\n                    )}\n                  </button>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-2 text-center\">\n                  Press Enter to send, Shift+Enter for new line\n                </p>\n              </div>\n            </div>\n          </>\n        )}\n\n        {step === 'conversation' && treatmentSession && (\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle>AI Medical Consultation</CardTitle>\n                  <CardDescription>\n                    Have a natural conversation with our AI assistant about your condition\n                  </CardDescription>\n                </div>\n                {uploadedImage && (\n                  <div className=\"text-center\">\n                    <img\n                      src={uploadedImage}\n                      alt=\"Uploaded condition\"\n                      className=\"w-16 h-16 rounded-lg border object-cover\"\n                    />\n                    <p className=\"text-xs text-muted-foreground mt-1\">Your image</p>\n                  </div>\n                )}\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50\">\n                {treatmentMessages.length === 0 ? (\n                  <div className=\"text-center text-muted-foreground py-8\">\n                    <ClipboardDocumentListIcon className=\"mx-auto h-12 w-12 mb-4\" />\n                    <p>The AI is analyzing your image...</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {treatmentMessages.map((message, index) => (\n                      <div\n                        key={index}\n                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${\n                            message.role === 'user'\n                              ? 'bg-primary text-primary-foreground'\n                              : 'bg-white border text-card-foreground shadow-sm'\n                          }`}\n                        >\n                          <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                          <p className=\"text-xs opacity-70 mt-2\">\n                            {new Date(message.timestamp).toLocaleTimeString()}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex space-x-2\">\n                <Input\n                  value={treatmentInput}\n                  onChange={(e) => setTreatmentInput(e.target.value)}\n                  placeholder=\"Describe your symptoms, ask questions, or share more details...\"\n                  className=\"flex-1\"\n                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && sendTreatmentMessage()}\n                />\n                <Button\n                  onClick={sendTreatmentMessage}\n                  disabled={!treatmentInput.trim() || loading}\n                  loading={loading}\n                >\n                  Send\n                </Button>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Start New Consultation\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {step === 'chat' && (\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardHeader>\n              <CardTitle>AI Health Assistant</CardTitle>\n              <CardDescription>\n                Chat with our AI for health advice and wellness guidance\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50\">\n                {chatMessages.length === 0 ? (\n                  <div className=\"text-center text-muted-foreground py-8\">\n                    <ChatBubbleLeftRightIcon className=\"mx-auto h-12 w-12 mb-4\" />\n                    <p>Start a conversation with our AI assistant</p>\n                    <p className=\"text-sm\">Ask about symptoms, health tips, or general wellness advice</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {chatMessages.map((message, index) => (\n                      <div\n                        key={index}\n                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                            message.role === 'user'\n                              ? 'bg-primary text-primary-foreground'\n                              : 'bg-white border text-card-foreground'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.content}</p>\n                          <p className=\"text-xs opacity-70 mt-1\">\n                            {new Date(message.timestamp).toLocaleTimeString()}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex space-x-2\">\n                <Input\n                  value={chatInput}\n                  onChange={(e) => setChatInput(e.target.value)}\n                  placeholder=\"Type your message...\"\n                  className=\"flex-1\"\n                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}\n                />\n                <Button\n                  onClick={sendChatMessage}\n                  disabled={!chatInput.trim() || loading}\n                  loading={loading}\n                >\n                  Send\n                </Button>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Start Over\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {step === 'completed' && treatmentSession && (\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardHeader className=\"text-center\">\n              <div className=\"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4\">\n                <CheckCircleIcon className=\"w-8 h-8 text-green-600\" />\n              </div>\n              <CardTitle>Assessment Complete</CardTitle>\n              <CardDescription>\n                Based on your responses, here are your personalized treatment recommendations\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"bg-card border border-border rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 text-card-foreground\">Treatment Recommendations</h3>\n                <div className=\"prose max-w-none\">\n                  <div className=\"whitespace-pre-line text-card-foreground\">\n                    {treatmentSession.treatment}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-5 w-5 text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-blue-800\">Important Note</h3>\n                    <div className=\"mt-2 text-sm text-blue-700\">\n                      <p>These recommendations are AI-generated and should be used as guidance only. Please consult with a healthcare professional for proper diagnosis and treatment.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Start New Assessment\n                </Button>\n                <Button onClick={() => window.print()} className=\"flex-1\">\n                  Save/Print Results\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AAtBA;;;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IACrF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIrD,EAAE;IACN,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF;QACF;kCAAG;QAAC;KAAgB;IAEpB,MAAM,wBAAwB;QAC5B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,YAAY;YAChD,oBAAoB;YACpB,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,kBAAkB;QAEhC,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QACX,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,OAAO;gBACrB,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,iBAAiB;gBAEjB,IAAI;oBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;wBAC1C,YAAY,iBAAiB,UAAU;wBACvC,YAAY;oBACd;oBAEA,oBAAoB;oBAEpB,2CAA2C;oBAC3C,IAAI,KAAK,oBAAoB,EAAE;wBAC7B,MAAM,WAAW,KAAK,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;gCAC5D,GAAG,GAAG;gCACN,WAAW,IAAI,OAAO,WAAW;4BACnC,CAAC;wBACD,qBAAqB;oBACvB;oBAEA,QAAQ;oBACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAChB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACd,SAAU;oBACR,WAAW;gBACb;YACF;YACA,OAAO,aAAa,CAAC;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa;YAC5C,iBAAiB,SAAS,UAAU;YACpC,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,oBAAoB,CAAC,eAAe,IAAI,IAAI;QAEjD,MAAM,cAAc;YAClB,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,qBAAqB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QACnD,MAAM,eAAe;QACrB,kBAAkB;QAClB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBAC9C,YAAY,iBAAiB,UAAU;gBACvC,SAAS;YACX;YAEA,MAAM,YAAY;gBAChB,MAAM;gBACN,SAAS,SAAS,WAAW;gBAC7B,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,qBAAqB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YAEjD,0CAA0C;YAC1C,oBAAoB,CAAA,OAAQ,OAAO;oBACjC,GAAG,IAAI;oBACP,sBAAsB,SAAS,oBAAoB;gBACrD,IAAI;QAEN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,eAAe;QAEzC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC9C,MAAM,eAAe;QACrB,aAAa;QACb,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBACzC,YAAY;gBACZ,SAAS;YACX;YAEA,MAAM,YAAyB;gBAC7B,MAAM;gBACN,SAAS,SAAS,QAAQ;gBAC1B,WAAW,SAAS,SAAS;YAC/B;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,oBAAoB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB,EAAE;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB,EAAE;QACvB,QAAQ;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAGzC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;oBAAiD,SAAS,yBACvE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;;8DAEV,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAGvC,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;;8DAEV,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DACnC,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAIzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAsC,SAAQ;gDAAY,MAAK;0DAC5E,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoN,UAAS;;;;;;;;;;;0DAE1P,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUrD,SAAS,kBAAkB,kCAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,aAAa,OAAO,EAAE;;sDAErC,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;wCAEX,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,0IAAA,CAAA,cAAW;wDAAC,MAAK;wDAAK,OAAM;;;;;;;;;;;8DAE/B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;mDAE7B,8BACF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;iEAG5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;8DAC5B,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASvD,CAAC,SAAS,kBAAkB,SAAS,MAAM,mBAC1C;;0CAEE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCAEZ,SAAS,kBAAkB,+BAC1B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK;wDACL,KAAI;wDACJ,WAAU;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;;;;;;;;;;;;wCAM3D,CAAC,SAAS,iBAAiB,oBAAoB,YAAY,EAAE,MAAM,KAAK,KAAK,CAAC,wBAC7E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC;oDAAG,WAAU;8DACX,SAAS,iBAAiB,kCAAkC;;;;;;8DAE/D,6LAAC;oDAAE,WAAU;8DACV,SAAS,iBACN,yDACA;;;;;;;;;;;iEAKR;;gDACG,CAAC,SAAS,iBAAiB,oBAAoB,YAAY,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC1E,6LAAC;wDAAgB,WAAU;;0EAEzB,6LAAC;gEAAI,WAAW,CAAC,oEAAoE,EACnF,QAAQ,IAAI,KAAK,SACb,gBACA,gBACJ;0EACC,QAAQ,IAAI,KAAK,uBAChB,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;yFAEpB,6LAAC,0NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAK5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,QAAQ,IAAI,KAAK,SAAS,QAAQ;;;;;;0FAErC,6LAAC;gFAAK,WAAU;0FACb,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;kFAGnD,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAE,WAAU;sFACV,QAAQ,OAAO;;;;;;;;;;;;;;;;;;uDA1Bd;;;;;gDAkCX,yBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,0IAAA,CAAA,cAAW;wEAAC,MAAK;wEAAK,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAW7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,OAAO,SAAS,iBAAiB,iBAAiB;wDAClD,UAAU,CAAC;4DACT,IAAI,SAAS,gBAAgB;gEAC3B,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DAClC,OAAO;gEACL,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC7B;wDACF;wDACA,WAAW,CAAC;4DACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;gEACpC,EAAE,cAAc;gEAChB,IAAI,SAAS,gBAAgB;oEAC3B;gEACF,OAAO;oEACL;gEACF;4DACF;wDACF;wDACA,aAAY;wDACZ,WAAU;wDACV,MAAM;wDACN,OAAO;4DAAE,WAAW;wDAAO;;;;;;;;;;;8DAG/B,6LAAC;oDACC,SAAS,SAAS,iBAAiB,uBAAuB;oDAC1D,UACE,WACA,CAAC,SAAS,iBAAiB,CAAC,eAAe,IAAI,KAAK,CAAC,UAAU,IAAI,EAAE;oDAEvE,WAAU;8DAET,wBACC,6LAAC,0IAAA,CAAA,cAAW;wDAAC,MAAK;wDAAK,OAAM;;;;;6EAE7B,6LAAC,oOAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAInC,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;;oBAQ7D,SAAS,kBAAkB,kCAC1B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;wCAIlB,+BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;0CAK1D,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;8DACrC,6LAAC;8DAAE;;;;;;;;;;;iEAGL,6LAAC;4CAAI,WAAU;sDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;oDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;8DAE9E,cAAA,6LAAC;wDACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,uCACA,kDACJ;;0EAEF,6LAAC;gEAAE,WAAU;0EAA+B,QAAQ,OAAO;;;;;;0EAC3D,6LAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;mDAZ9C;;;;;;;;;;;;;;;kDAqBf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,aAAY;gDACZ,WAAU;gDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI;;;;;;0DAEzD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC,eAAe,IAAI,MAAM;gDACpC,SAAS;0DACV;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;oBAQ3E,SAAS,wBACR,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM,KAAK,kBACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DACnC,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;iEAGzB,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;oDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;8DAE9E,cAAA,6LAAC;wDACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,uCACA,wCACJ;;0EAEF,6LAAC;gEAAE,WAAU;0EAAW,QAAQ,OAAO;;;;;;0EACvC,6LAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;mDAZ9C;;;;;;;;;;;;;;;kDAqBf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,aAAY;gDACZ,WAAU;gDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0DAE1C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC,UAAU,IAAI,MAAM;gDAC/B,SAAS;0DACV;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;oBAQ3E,SAAS,eAAe,kCACvB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,SAAS;;;;;;;;;;;;;;;;;kDAKjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAmI,UAAS;;;;;;;;;;;;;;;;8DAG3K,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,SAAQ;gDAAU,WAAU;0DAAS;;;;;;0DAGpE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,OAAO,KAAK;gDAAI,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E;GA1qBwB;;QACM,kIAAA,CAAA,UAAO;;;KADb", "debugId": null}}]}