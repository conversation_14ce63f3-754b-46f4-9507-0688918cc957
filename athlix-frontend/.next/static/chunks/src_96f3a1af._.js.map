{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function formatTime(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function getInitials(firstName: string, lastName: string): string {\n  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // UUID v4 regex\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(sessionId);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,YAAY,SAAiB,EAAE,QAAgB;IAC7D,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;AAClE;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,iBAAiB,SAAiB;IAChD,gBAAgB;IAChB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';\n    \n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow',\n      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow',\n      outline: 'border border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground shadow-sm',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',\n      ghost: 'text-foreground hover:bg-accent hover:text-accent-foreground',\n      link: 'underline-offset-4 hover:underline text-primary',\n    };\n\n    const sizes = {\n      default: 'h-10 py-2 px-4',\n      sm: 'h-9 px-3 rounded-md',\n      lg: 'h-11 px-8 rounded-md',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/Button';\nimport { \n  HeartIcon, \n  UserIcon, \n  ChatBubbleLeftIcon, \n  ClipboardDocumentListIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { getInitials } from '@/lib/utils';\n\nexport function Navbar() {\n  const { user, logout, isAuthenticated } = useAuth();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Treatment', href: '/treatment', icon: ClipboardDocumentListIcon },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg\">\n                <HeartIcon className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">ATHLIX</span>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          {isAuthenticated && (\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-1 text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              ))}\n            </div>\n          )}\n\n          {/* User menu */}\n          <div className=\"flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-3\">\n                {/* User avatar */}\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-blue-600\">\n                      {user ? getInitials(user.first_name, user.last_name) : 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm text-gray-700\">\n                    {user?.first_name} {user?.last_name}\n                  </span>\n                </div>\n                \n                {/* Logout button */}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={logout}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"w-4 h-4\" />\n                  <span className=\"hidden sm:ml-2 sm:block\">Logout</span>\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    Login\n                  </Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button size=\"sm\">\n                    Sign Up\n                  </Button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            {isAuthenticated && (\n              <div className=\"md:hidden\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                >\n                  {mobileMenuOpen ? (\n                    <XMarkIcon className=\"w-5 h-5\" />\n                  ) : (\n                    <Bars3Icon className=\"w-5 h-5\" />\n                  )}\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isAuthenticated && mobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <item.icon className=\"w-5 h-5\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAfA;;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,oPAAA,CAAA,4BAAyB;QAAC;KAC1E;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;wBAKrD,iCACC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAYtB,6LAAC;4BAAI,WAAU;;gCACZ,gCACC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,OAAO,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;8DAG3D,6LAAC;oDAAK,WAAU;;wDACb,MAAM;wDAAW;wDAAE,MAAM;;;;;;;;;;;;;sDAK9B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,oPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;8DACrC,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;yDAI9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAAK;;;;;;;;;;;sDAIpC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;gCAQvB,iCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,kBAAkB,CAAC;kDAEjC,+BACC,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,mBAAmB,gCAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAM,KAAK,IAAI;;;;;;;2BANX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAc9B;GAxHgB;;QAC4B,kIAAA,CAAA,UAAO;;;KADnC", "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium leading-none text-foreground peer-disabled:cursor-not-allowed peer-disabled:opacity-70\">\n            {label}\n          </label>\n        )}\n        <input\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-md border border-input bg-background text-foreground px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-destructive focus-visible:ring-destructive',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"text-sm text-muted-foreground\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gXACA,SAAS,qDACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;YAE1C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border border-border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight text-card-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/app/treatment/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Navbar } from '@/components/layout/Navbar';\nimport { Button } from '@/components/ui/Button';\nimport { Input } from '@/components/ui/Input';\nimport { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';\nimport {\n  ClipboardDocumentListIcon,\n  PhotoIcon,\n  CheckCircleIcon,\n  ArrowRightIcon,\n  CloudArrowUpIcon,\n  ChatBubbleLeftRightIcon,\n  PaperAirplaneIcon,\n  PlusIcon,\n  UserIcon,\n  SparklesIcon\n} from '@heroicons/react/24/outline';\nimport { treatmentAPI, chatAPI } from '@/lib/api';\nimport toast from 'react-hot-toast';\nimport { LoadingDots, TypingIndicator } from '@/components/ui/LoadingDots';\n\ninterface TreatmentSession {\n  session_id: string;\n  status: 'image_upload' | 'conversation' | 'completed';\n  ai_response?: string;\n  conversation_history?: Array<{\n    role: 'user' | 'assistant';\n    content: string;\n  }>;\n  message?: string;\n}\n\ninterface ChatMessage {\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: string;\n}\n\nexport default function TreatmentPage() {\n  const { isAuthenticated } = useAuth();\n  const [treatmentSession, setTreatmentSession] = useState<TreatmentSession | null>(null);\n  const [currentAnswer, setCurrentAnswer] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState<'start' | 'image_upload' | 'conversation' | 'chat'>('start');\n  const [uploadedImage, setUploadedImage] = useState<string | null>(null);\n  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);\n  const [chatInput, setChatInput] = useState('');\n  const [chatSessionId, setChatSessionId] = useState<string | null>(null);\n  const [treatmentInput, setTreatmentInput] = useState('');\n  const [treatmentMessages, setTreatmentMessages] = useState<Array<{\n    role: 'user' | 'assistant';\n    content: string;\n    timestamp: string;\n  }>>([]);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    if (!isAuthenticated) {\n      window.location.href = '/login';\n      return;\n    }\n  }, [isAuthenticated]);\n\n  const startTreatmentSession = async () => {\n    setLoading(true);\n    try {\n      const response = await treatmentAPI.startSession();\n      setTreatmentSession(response);\n      setStep('image_upload');\n      toast.success('Treatment session started - please upload an image');\n    } catch (error) {\n      console.error('Error starting treatment session:', error);\n      toast.error('Failed to start treatment session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file || !treatmentSession) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      toast.error('Please upload an image file');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('Image size should be less than 5MB');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Convert to base64\n      const reader = new FileReader();\n      reader.onload = async (e) => {\n        const base64 = e.target?.result as string;\n        setUploadedImage(base64);\n\n        try {\n          const data = await treatmentAPI.uploadImage({\n            session_id: treatmentSession.session_id,\n            image_data: base64,\n          });\n\n          setTreatmentSession(data);\n\n          // Initialize conversation with AI response\n          if (data.conversation_history) {\n            const messages = data.conversation_history.map((msg: any) => ({\n              ...msg,\n              timestamp: new Date().toISOString()\n            }));\n            setTreatmentMessages(messages);\n          }\n\n          setStep('conversation');\n          toast.success('Image uploaded and analyzed! You can now chat with our AI assistant.');\n        } catch (error) {\n          console.error('Error uploading image:', error);\n          toast.error('Failed to upload image');\n        } finally {\n          setLoading(false);\n        }\n      };\n      reader.readAsDataURL(file);\n    } catch (error) {\n      console.error('Error processing image:', error);\n      toast.error('Failed to process image');\n      setLoading(false);\n    }\n  };\n\n  const startDirectChat = async () => {\n    setLoading(true);\n    try {\n      const response = await chatAPI.createSession();\n      setChatSessionId(response.session_id);\n      setStep('chat');\n      toast.success('Chat session started');\n    } catch (error) {\n      console.error('Error starting chat session:', error);\n      toast.error('Failed to start chat session');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendTreatmentMessage = async () => {\n    if (!treatmentSession || !treatmentInput.trim()) return;\n\n    const userMessage = {\n      role: 'user' as const,\n      content: treatmentInput,\n      timestamp: new Date().toISOString(),\n    };\n\n    setTreatmentMessages(prev => [...prev, userMessage]);\n    const currentInput = treatmentInput;\n    setTreatmentInput('');\n    setLoading(true);\n\n    try {\n      const response = await treatmentAPI.sendMessage({\n        session_id: treatmentSession.session_id,\n        message: currentInput,\n      });\n\n      const aiMessage = {\n        role: 'assistant' as const,\n        content: response.ai_response,\n        timestamp: new Date().toISOString(),\n      };\n\n      setTreatmentMessages(prev => [...prev, aiMessage]);\n\n      // Update session with latest conversation\n      setTreatmentSession(prev => prev ? {\n        ...prev,\n        conversation_history: response.conversation_history\n      } : null);\n\n    } catch (error) {\n      console.error('Error sending treatment message:', error);\n      toast.error('Failed to send message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendChatMessage = async () => {\n    if (!chatInput.trim() || !chatSessionId) return;\n\n    const userMessage: ChatMessage = {\n      role: 'user',\n      content: chatInput,\n      timestamp: new Date().toISOString(),\n    };\n\n    setChatMessages(prev => [...prev, userMessage]);\n    const currentInput = chatInput;\n    setChatInput('');\n    setLoading(true);\n\n    try {\n      const response = await chatAPI.sendMessage({\n        session_id: chatSessionId,\n        message: currentInput,\n      });\n\n      const aiMessage: ChatMessage = {\n        role: 'assistant',\n        content: response.response,\n        timestamp: response.timestamp,\n      };\n\n      setChatMessages(prev => [...prev, aiMessage]);\n    } catch (error) {\n      console.error('Error sending chat message:', error);\n      toast.error('Failed to send message');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetSession = () => {\n    setTreatmentSession(null);\n    setCurrentAnswer('');\n    setUploadedImage(null);\n    setChatMessages([]);\n    setChatInput('');\n    setChatSessionId(null);\n    setTreatmentInput('');\n    setTreatmentMessages([]);\n    setStep('start');\n  };\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-foreground\">ATHLIX AI Assistant</h1>\n          <p className=\"mt-2 text-muted-foreground\">\n            Upload an image for treatment assessment or chat directly with our AI\n          </p>\n        </div>\n\n        {step === 'start' && (\n          <div className=\"max-w-4xl mx-auto space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Image-based Treatment */}\n              <Card>\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4\">\n                    <PhotoIcon className=\"w-8 h-8 text-blue-600\" />\n                  </div>\n                  <CardTitle>Image-Based Assessment</CardTitle>\n                  <CardDescription>\n                    Upload an image of your condition and our AI will ask targeted questions for personalized treatment recommendations.\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                      <CloudArrowUpIcon className=\"w-4 h-4\" />\n                      <span>Upload image of affected area</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                      <ClipboardDocumentListIcon className=\"w-4 h-4\" />\n                      <span>Answer AI-generated questions</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                      <CheckCircleIcon className=\"w-4 h-4\" />\n                      <span>Get personalized treatment plan</span>\n                    </div>\n                  </div>\n                  <Button\n                    onClick={startTreatmentSession}\n                    loading={loading}\n                    className=\"w-full\"\n                    size=\"lg\"\n                  >\n                    Start Image Assessment\n                  </Button>\n                </CardContent>\n              </Card>\n\n              {/* Direct Chat */}\n              <Card>\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4\">\n                    <ChatBubbleLeftRightIcon className=\"w-8 h-8 text-green-600\" />\n                  </div>\n                  <CardTitle>Direct AI Chat</CardTitle>\n                  <CardDescription>\n                    Chat directly with our AI assistant for immediate health advice and general wellness guidance.\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                      <ChatBubbleLeftRightIcon className=\"w-4 h-4\" />\n                      <span>Instant AI responses</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                      <ClipboardDocumentListIcon className=\"w-4 h-4\" />\n                      <span>Health advice and tips</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                      <CheckCircleIcon className=\"w-4 h-4\" />\n                      <span>24/7 availability</span>\n                    </div>\n                  </div>\n                  <Button\n                    onClick={startDirectChat}\n                    loading={loading}\n                    className=\"w-full\"\n                    size=\"lg\"\n                    variant=\"outline\"\n                  >\n                    Start AI Chat\n                  </Button>\n                </CardContent>\n              </Card>\n            </div>\n\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-yellow-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-yellow-800\">Medical Disclaimer</h3>\n                  <div className=\"mt-2 text-sm text-yellow-700\">\n                    <p>This service is for informational purposes only and should not replace professional medical advice. Always consult with a healthcare provider for serious medical concerns.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {step === 'image_upload' && treatmentSession && (\n          <Card className=\"max-w-2xl mx-auto\">\n            <CardHeader className=\"text-center\">\n              <div className=\"mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4\">\n                <PhotoIcon className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <CardTitle>Upload Image</CardTitle>\n              <CardDescription>\n                Please upload a clear image of the affected area for AI analysis\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\">\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageUpload}\n                  className=\"hidden\"\n                />\n                {uploadedImage ? (\n                  <div className=\"space-y-4\">\n                    <img\n                      src={uploadedImage}\n                      alt=\"Uploaded\"\n                      className=\"max-w-full max-h-64 mx-auto rounded-lg\"\n                    />\n                    <p className=\"text-sm text-green-600\">Image uploaded successfully!</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <CloudArrowUpIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div>\n                      <p className=\"text-lg font-medium\">Upload an image</p>\n                      <p className=\"text-sm text-muted-foreground\">PNG, JPG, GIF up to 5MB</p>\n                    </div>\n                    <Button\n                      onClick={() => fileInputRef.current?.click()}\n                      loading={loading}\n                      className=\"mt-4\"\n                    >\n                      Choose Image\n                    </Button>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Back\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {step === 'conversation' && treatmentSession && (\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle>AI Medical Consultation</CardTitle>\n                  <CardDescription>\n                    Have a natural conversation with our AI assistant about your condition\n                  </CardDescription>\n                </div>\n                {uploadedImage && (\n                  <div className=\"text-center\">\n                    <img\n                      src={uploadedImage}\n                      alt=\"Uploaded condition\"\n                      className=\"w-16 h-16 rounded-lg border object-cover\"\n                    />\n                    <p className=\"text-xs text-muted-foreground mt-1\">Your image</p>\n                  </div>\n                )}\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50\">\n                {treatmentMessages.length === 0 ? (\n                  <div className=\"text-center text-muted-foreground py-8\">\n                    <ClipboardDocumentListIcon className=\"mx-auto h-12 w-12 mb-4\" />\n                    <p>The AI is analyzing your image...</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {treatmentMessages.map((message, index) => (\n                      <div\n                        key={index}\n                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${\n                            message.role === 'user'\n                              ? 'bg-primary text-primary-foreground'\n                              : 'bg-white border text-card-foreground shadow-sm'\n                          }`}\n                        >\n                          <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                          <p className=\"text-xs opacity-70 mt-2\">\n                            {new Date(message.timestamp).toLocaleTimeString()}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex space-x-2\">\n                <Input\n                  value={treatmentInput}\n                  onChange={(e) => setTreatmentInput(e.target.value)}\n                  placeholder=\"Describe your symptoms, ask questions, or share more details...\"\n                  className=\"flex-1\"\n                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && sendTreatmentMessage()}\n                />\n                <Button\n                  onClick={sendTreatmentMessage}\n                  disabled={!treatmentInput.trim() || loading}\n                  loading={loading}\n                >\n                  Send\n                </Button>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Start New Consultation\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {step === 'chat' && (\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardHeader>\n              <CardTitle>AI Health Assistant</CardTitle>\n              <CardDescription>\n                Chat with our AI for health advice and wellness guidance\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50\">\n                {chatMessages.length === 0 ? (\n                  <div className=\"text-center text-muted-foreground py-8\">\n                    <ChatBubbleLeftRightIcon className=\"mx-auto h-12 w-12 mb-4\" />\n                    <p>Start a conversation with our AI assistant</p>\n                    <p className=\"text-sm\">Ask about symptoms, health tips, or general wellness advice</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {chatMessages.map((message, index) => (\n                      <div\n                        key={index}\n                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                            message.role === 'user'\n                              ? 'bg-primary text-primary-foreground'\n                              : 'bg-white border text-card-foreground'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.content}</p>\n                          <p className=\"text-xs opacity-70 mt-1\">\n                            {new Date(message.timestamp).toLocaleTimeString()}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex space-x-2\">\n                <Input\n                  value={chatInput}\n                  onChange={(e) => setChatInput(e.target.value)}\n                  placeholder=\"Type your message...\"\n                  className=\"flex-1\"\n                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}\n                />\n                <Button\n                  onClick={sendChatMessage}\n                  disabled={!chatInput.trim() || loading}\n                  loading={loading}\n                >\n                  Send\n                </Button>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Start Over\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {step === 'completed' && treatmentSession && (\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardHeader className=\"text-center\">\n              <div className=\"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4\">\n                <CheckCircleIcon className=\"w-8 h-8 text-green-600\" />\n              </div>\n              <CardTitle>Assessment Complete</CardTitle>\n              <CardDescription>\n                Based on your responses, here are your personalized treatment recommendations\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"bg-card border border-border rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 text-card-foreground\">Treatment Recommendations</h3>\n                <div className=\"prose max-w-none\">\n                  <div className=\"whitespace-pre-line text-card-foreground\">\n                    {treatmentSession.treatment}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-5 w-5 text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-blue-800\">Important Note</h3>\n                    <div className=\"mt-2 text-sm text-blue-700\">\n                      <p>These recommendations are AI-generated and should be used as guidance only. Please consult with a healthcare professional for proper diagnosis and treatment.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button onClick={resetSession} variant=\"outline\" className=\"flex-1\">\n                  Start New Assessment\n                </Button>\n                <Button onClick={() => window.print()} className=\"flex-1\">\n                  Save/Print Results\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AArBA;;;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IACrF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIrD,EAAE;IACN,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACF;QACF;kCAAG;QAAC;KAAgB;IAEpB,MAAM,wBAAwB;QAC5B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,YAAY;YAChD,oBAAoB;YACpB,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,QAAQ,CAAC,kBAAkB;QAEhC,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QACX,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,OAAO;gBACrB,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,iBAAiB;gBAEjB,IAAI;oBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;wBAC1C,YAAY,iBAAiB,UAAU;wBACvC,YAAY;oBACd;oBAEA,oBAAoB;oBAEpB,2CAA2C;oBAC3C,IAAI,KAAK,oBAAoB,EAAE;wBAC7B,MAAM,WAAW,KAAK,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;gCAC5D,GAAG,GAAG;gCACN,WAAW,IAAI,OAAO,WAAW;4BACnC,CAAC;wBACD,qBAAqB;oBACvB;oBAEA,QAAQ;oBACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAChB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACd,SAAU;oBACR,WAAW;gBACb;YACF;YACA,OAAO,aAAa,CAAC;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa;YAC5C,iBAAiB,SAAS,UAAU;YACpC,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,oBAAoB,CAAC,eAAe,IAAI,IAAI;QAEjD,MAAM,cAAc;YAClB,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,qBAAqB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QACnD,MAAM,eAAe;QACrB,kBAAkB;QAClB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBAC9C,YAAY,iBAAiB,UAAU;gBACvC,SAAS;YACX;YAEA,MAAM,YAAY;gBAChB,MAAM;gBACN,SAAS,SAAS,WAAW;gBAC7B,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,qBAAqB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YAEjD,0CAA0C;YAC1C,oBAAoB,CAAA,OAAQ,OAAO;oBACjC,GAAG,IAAI;oBACP,sBAAsB,SAAS,oBAAoB;gBACrD,IAAI;QAEN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,eAAe;QAEzC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC9C,MAAM,eAAe;QACrB,aAAa;QACb,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBACzC,YAAY;gBACZ,SAAS;YACX;YAEA,MAAM,YAAyB;gBAC7B,MAAM;gBACN,SAAS,SAAS,QAAQ;gBAC1B,WAAW,SAAS,SAAS;YAC/B;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,oBAAoB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB,EAAE;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB,EAAE;QACvB,QAAQ;IACV;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAK3C,SAAS,yBACR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,kOAAA,CAAA,mBAAgB;wEAAC,WAAU;;;;;;kFAC5B,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oPAAA,CAAA,4BAAyB;wEAAC,WAAU;;;;;;kFACrC,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,gOAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;kFAC3B,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAGV,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,SAAS;wDACT,WAAU;wDACV,MAAK;kEACN;;;;;;;;;;;;;;;;;;kDAOL,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gPAAA,CAAA,0BAAuB;4DAAC,WAAU;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,gPAAA,CAAA,0BAAuB;wEAAC,WAAU;;;;;;kFACnC,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oPAAA,CAAA,4BAAyB;wEAAC,WAAU;;;;;;kFACrC,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,gOAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;kFAC3B,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAGV,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,SAAS;wDACT,WAAU;wDACV,MAAK;wDACL,SAAQ;kEACT;;;;;;;;;;;;;;;;;;;;;;;;0CAOP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA0B,SAAQ;gDAAY,MAAK;0DAChE,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoN,UAAS;;;;;;;;;;;;;;;;sDAG5P,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQd,SAAS,kBAAkB,kCAC1B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK;gDACL,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,WAAU;;;;;;4CAEX,8BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK;wDACL,KAAI;wDACJ,WAAU;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;qEAGxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,kOAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;kEAC5B,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,aAAa,OAAO,EAAE;wDACrC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAOP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;oBAQ3E,SAAS,kBAAkB,kCAC1B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;wCAIlB,+BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;0CAK1D,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;8DACrC,6LAAC;8DAAE;;;;;;;;;;;iEAGL,6LAAC;4CAAI,WAAU;sDACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;oDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;8DAE9E,cAAA,6LAAC;wDACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,uCACA,kDACJ;;0EAEF,6LAAC;gEAAE,WAAU;0EAA+B,QAAQ,OAAO;;;;;;0EAC3D,6LAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;mDAZ9C;;;;;;;;;;;;;;;kDAqBf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,aAAY;gDACZ,WAAU;gDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI;;;;;;0DAEzD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC,eAAe,IAAI,MAAM;gDACpC,SAAS;0DACV;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;oBAQ3E,SAAS,wBACR,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM,KAAK,kBACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DACnC,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;iEAGzB,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;oDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;8DAE9E,cAAA,6LAAC;wDACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,uCACA,wCACJ;;0EAEF,6LAAC;gEAAE,WAAU;0EAAW,QAAQ,OAAO;;;;;;0EACvC,6LAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;mDAZ9C;;;;;;;;;;;;;;;kDAqBf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,aAAY;gDACZ,WAAU;gDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0DAE1C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC,UAAU,IAAI,MAAM;gDAC/B,SAAS;0DACV;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAc,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;oBAQ3E,SAAS,eAAe,kCACvB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,SAAS;;;;;;;;;;;;;;;;;kDAKjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAmI,UAAS;;;;;;;;;;;;;;;;8DAG3K,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,SAAQ;gDAAU,WAAU;0DAAS;;;;;;0DAGpE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,OAAO,KAAK;gDAAI,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E;GAzjBwB;;QACM,kIAAA,CAAA,UAAO;;;KADb", "debugId": null}}]}