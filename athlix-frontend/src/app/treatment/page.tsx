'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/layout/Navbar';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import {
  ClipboardDocumentListIcon,
  PhotoIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  CloudArrowUpIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { treatmentAPI, chatAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface TreatmentSession {
  session_id: string;
  status: 'image_upload' | 'conversation' | 'completed';
  ai_response?: string;
  conversation_history?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  message?: string;
}

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export default function TreatmentPage() {
  const { isAuthenticated } = useAuth();
  const [treatmentSession, setTreatmentSession] = useState<TreatmentSession | null>(null);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'start' | 'image_upload' | 'conversation' | 'chat'>('start');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [chatSessionId, setChatSessionId] = useState<string | null>(null);
  const [treatmentInput, setTreatmentInput] = useState('');
  const [treatmentMessages, setTreatmentMessages] = useState<Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }>>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
  }, [isAuthenticated]);

  const startTreatmentSession = async () => {
    setLoading(true);
    try {
      const response = await treatmentAPI.startSession();
      setTreatmentSession(response);
      setStep('image_upload');
      toast.success('Treatment session started - please upload an image');
    } catch (error) {
      console.error('Error starting treatment session:', error);
      toast.error('Failed to start treatment session');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !treatmentSession) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please upload an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB');
      return;
    }

    setLoading(true);
    try {
      // Convert to base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        setUploadedImage(base64);

        try {
          const data = await treatmentAPI.uploadImage({
            session_id: treatmentSession.session_id,
            image_data: base64,
          });

          setTreatmentSession(data);

          // Initialize conversation with AI response
          if (data.conversation_history) {
            const messages = data.conversation_history.map((msg: any) => ({
              ...msg,
              timestamp: new Date().toISOString()
            }));
            setTreatmentMessages(messages);
          }

          setStep('conversation');
          toast.success('Image uploaded and analyzed! You can now chat with our AI assistant.');
        } catch (error) {
          console.error('Error uploading image:', error);
          toast.error('Failed to upload image');
        } finally {
          setLoading(false);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing image:', error);
      toast.error('Failed to process image');
      setLoading(false);
    }
  };

  const startDirectChat = async () => {
    setLoading(true);
    try {
      const response = await chatAPI.createSession();
      setChatSessionId(response.session_id);
      setStep('chat');
      toast.success('Chat session started');
    } catch (error) {
      console.error('Error starting chat session:', error);
      toast.error('Failed to start chat session');
    } finally {
      setLoading(false);
    }
  };

  const sendTreatmentMessage = async () => {
    if (!treatmentSession || !treatmentInput.trim()) return;

    const userMessage = {
      role: 'user' as const,
      content: treatmentInput,
      timestamp: new Date().toISOString(),
    };

    setTreatmentMessages(prev => [...prev, userMessage]);
    const currentInput = treatmentInput;
    setTreatmentInput('');
    setLoading(true);

    try {
      const response = await treatmentAPI.sendMessage({
        session_id: treatmentSession.session_id,
        message: currentInput,
      });

      const aiMessage = {
        role: 'assistant' as const,
        content: response.ai_response,
        timestamp: new Date().toISOString(),
      };

      setTreatmentMessages(prev => [...prev, aiMessage]);

      // Update session with latest conversation
      setTreatmentSession(prev => prev ? {
        ...prev,
        conversation_history: response.conversation_history
      } : null);

    } catch (error) {
      console.error('Error sending treatment message:', error);
      toast.error('Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  const sendChatMessage = async () => {
    if (!chatInput.trim() || !chatSessionId) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: chatInput,
      timestamp: new Date().toISOString(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    const currentInput = chatInput;
    setChatInput('');
    setLoading(true);

    try {
      const response = await chatAPI.sendMessage({
        session_id: chatSessionId,
        message: currentInput,
      });

      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: response.response,
        timestamp: response.timestamp,
      };

      setChatMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending chat message:', error);
      toast.error('Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  const resetSession = () => {
    setTreatmentSession(null);
    setCurrentAnswer('');
    setUploadedImage(null);
    setChatMessages([]);
    setChatInput('');
    setChatSessionId(null);
    setTreatmentInput('');
    setTreatmentMessages([]);
    setStep('start');
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground">ATHLIX AI Assistant</h1>
          <p className="mt-2 text-muted-foreground">
            Upload an image for treatment assessment or chat directly with our AI
          </p>
        </div>

        {step === 'start' && (
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Image-based Treatment */}
              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <PhotoIcon className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle>Image-Based Assessment</CardTitle>
                  <CardDescription>
                    Upload an image of your condition and our AI will ask targeted questions for personalized treatment recommendations.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <CloudArrowUpIcon className="w-4 h-4" />
                      <span>Upload image of affected area</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <ClipboardDocumentListIcon className="w-4 h-4" />
                      <span>Answer AI-generated questions</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <CheckCircleIcon className="w-4 h-4" />
                      <span>Get personalized treatment plan</span>
                    </div>
                  </div>
                  <Button
                    onClick={startTreatmentSession}
                    loading={loading}
                    className="w-full"
                    size="lg"
                  >
                    Start Image Assessment
                  </Button>
                </CardContent>
              </Card>

              {/* Direct Chat */}
              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <ChatBubbleLeftRightIcon className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle>Direct AI Chat</CardTitle>
                  <CardDescription>
                    Chat directly with our AI assistant for immediate health advice and general wellness guidance.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <ChatBubbleLeftRightIcon className="w-4 h-4" />
                      <span>Instant AI responses</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <ClipboardDocumentListIcon className="w-4 h-4" />
                      <span>Health advice and tips</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <CheckCircleIcon className="w-4 h-4" />
                      <span>24/7 availability</span>
                    </div>
                  </div>
                  <Button
                    onClick={startDirectChat}
                    loading={loading}
                    className="w-full"
                    size="lg"
                    variant="outline"
                  >
                    Start AI Chat
                  </Button>
                </CardContent>
              </Card>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Medical Disclaimer</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>This service is for informational purposes only and should not replace professional medical advice. Always consult with a healthcare provider for serious medical concerns.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'image_upload' && treatmentSession && (
          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <PhotoIcon className="w-8 h-8 text-blue-600" />
              </div>
              <CardTitle>Upload Image</CardTitle>
              <CardDescription>
                Please upload a clear image of the affected area for AI analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                {uploadedImage ? (
                  <div className="space-y-4">
                    <img
                      src={uploadedImage}
                      alt="Uploaded"
                      className="max-w-full max-h-64 mx-auto rounded-lg"
                    />
                    <p className="text-sm text-green-600">Image uploaded successfully!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div>
                      <p className="text-lg font-medium">Upload an image</p>
                      <p className="text-sm text-muted-foreground">PNG, JPG, GIF up to 5MB</p>
                    </div>
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      loading={loading}
                      className="mt-4"
                    >
                      Choose Image
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex space-x-3">
                <Button onClick={resetSession} variant="outline" className="flex-1">
                  Back
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 'conversation' && treatmentSession && (
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>AI Medical Consultation</CardTitle>
                  <CardDescription>
                    Have a natural conversation with our AI assistant about your condition
                  </CardDescription>
                </div>
                {uploadedImage && (
                  <div className="text-center">
                    <img
                      src={uploadedImage}
                      alt="Uploaded condition"
                      className="w-16 h-16 rounded-lg border object-cover"
                    />
                    <p className="text-xs text-muted-foreground mt-1">Your image</p>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50">
                {treatmentMessages.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <ClipboardDocumentListIcon className="mx-auto h-12 w-12 mb-4" />
                    <p>The AI is analyzing your image...</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {treatmentMessages.map((message, index) => (
                      <div
                        key={index}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                            message.role === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-white border text-card-foreground shadow-sm'
                          }`}
                        >
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                          <p className="text-xs opacity-70 mt-2">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex space-x-2">
                <Input
                  value={treatmentInput}
                  onChange={(e) => setTreatmentInput(e.target.value)}
                  placeholder="Describe your symptoms, ask questions, or share more details..."
                  className="flex-1"
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && sendTreatmentMessage()}
                />
                <Button
                  onClick={sendTreatmentMessage}
                  disabled={!treatmentInput.trim() || loading}
                  loading={loading}
                >
                  Send
                </Button>
              </div>

              <div className="flex space-x-3">
                <Button onClick={resetSession} variant="outline" className="flex-1">
                  Start New Consultation
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 'chat' && (
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle>AI Health Assistant</CardTitle>
              <CardDescription>
                Chat with our AI for health advice and wellness guidance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50">
                {chatMessages.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 mb-4" />
                    <p>Start a conversation with our AI assistant</p>
                    <p className="text-sm">Ask about symptoms, health tips, or general wellness advice</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {chatMessages.map((message, index) => (
                      <div
                        key={index}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.role === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-white border text-card-foreground'
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className="text-xs opacity-70 mt-1">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex space-x-2">
                <Input
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1"
                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                />
                <Button
                  onClick={sendChatMessage}
                  disabled={!chatInput.trim() || loading}
                  loading={loading}
                >
                  Send
                </Button>
              </div>

              <div className="flex space-x-3">
                <Button onClick={resetSession} variant="outline" className="flex-1">
                  Start Over
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 'completed' && treatmentSession && (
          <Card className="max-w-4xl mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle>Assessment Complete</CardTitle>
              <CardDescription>
                Based on your responses, here are your personalized treatment recommendations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 text-card-foreground">Treatment Recommendations</h3>
                <div className="prose max-w-none">
                  <div className="whitespace-pre-line text-card-foreground">
                    {treatmentSession.treatment}
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Important Note</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>These recommendations are AI-generated and should be used as guidance only. Please consult with a healthcare professional for proper diagnosis and treatment.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button onClick={resetSession} variant="outline" className="flex-1">
                  Start New Assessment
                </Button>
                <Button onClick={() => window.print()} className="flex-1">
                  Save/Print Results
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
