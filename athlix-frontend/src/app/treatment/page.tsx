'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/layout/Navbar';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import { 
  ClipboardDocumentListIcon,
  PhotoIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { treatmentAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface Question {
  id: string;
  question: string;
  type: 'text' | 'number' | 'multiple_choice';
  options?: string[];
}

interface TreatmentSession {
  session_id: string;
  status: 'questioning' | 'completed';
  current_question?: Question;
  questions?: Question[];
  answers?: any[];
  treatment?: string;
  total_questions?: number;
  questions_remaining?: number;
}

export default function TreatmentPage() {
  const { isAuthenticated } = useAuth();
  const [treatmentSession, setTreatmentSession] = useState<TreatmentSession | null>(null);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'start' | 'questioning' | 'completed'>('start');

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
  }, [isAuthenticated]);

  const startTreatmentSession = async () => {
    setLoading(true);
    try {
      const response = await treatmentAPI.startSession();
      setTreatmentSession(response);
      setStep('questioning');
      toast.success('Treatment assessment started');
    } catch (error) {
      console.error('Error starting treatment session:', error);
      toast.error('Failed to start treatment session');
    } finally {
      setLoading(false);
    }
  };

  const submitAnswer = async () => {
    if (!treatmentSession || !currentAnswer.trim()) return;

    setLoading(true);
    try {
      const response = await treatmentAPI.answerQuestion({
        session_id: treatmentSession.session_id,
        question_id: treatmentSession.current_question?.id || '',
        answer: currentAnswer,
      });

      if (response.status === 'completed') {
        setTreatmentSession(prev => prev ? {
          ...prev,
          status: 'completed',
          treatment: response.treatment,
        } : null);
        setStep('completed');
        toast.success('Treatment assessment completed');
      } else {
        setTreatmentSession(prev => prev ? {
          ...prev,
          current_question: response.current_question,
          questions_remaining: response.questions_remaining,
        } : null);
        toast.success('Answer submitted');
      }
      
      setCurrentAnswer('');
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast.error('Failed to submit answer');
    } finally {
      setLoading(false);
    }
  };

  const handleOptionSelect = (option: string) => {
    setCurrentAnswer(option);
  };

  const resetSession = () => {
    setTreatmentSession(null);
    setCurrentAnswer('');
    setStep('start');
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground">Treatment Assessment</h1>
          <p className="mt-2 text-muted-foreground">
            Get personalized treatment recommendations based on your symptoms and condition
          </p>
        </div>

        {step === 'start' && (
          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <ClipboardDocumentListIcon className="w-8 h-8 text-blue-600" />
              </div>
              <CardTitle>Start Your Treatment Assessment</CardTitle>
              <CardDescription>
                Our AI will ask you a series of questions to understand your condition and provide personalized treatment recommendations.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border border-border rounded-lg bg-card">
                  <div className="flex items-center space-x-3">
                    <ClipboardDocumentListIcon className="w-6 h-6 text-blue-600" />
                    <div>
                      <h3 className="font-medium text-card-foreground">Symptom Analysis</h3>
                      <p className="text-sm text-muted-foreground">Answer questions about your symptoms</p>
                    </div>
                  </div>
                </div>
                <div className="p-4 border border-border rounded-lg bg-card">
                  <div className="flex items-center space-x-3">
                    <PhotoIcon className="w-6 h-6 text-green-600" />
                    <div>
                      <h3 className="font-medium text-card-foreground">Image Analysis</h3>
                      <p className="text-sm text-muted-foreground">Upload images for visual assessment</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Medical Disclaimer</h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>This assessment is for informational purposes only and should not replace professional medical advice. Always consult with a healthcare provider for serious medical concerns.</p>
                    </div>
                  </div>
                </div>
              </div>

              <Button 
                onClick={startTreatmentSession} 
                loading={loading}
                className="w-full"
                size="lg"
              >
                Start Assessment
              </Button>
            </CardContent>
          </Card>
        )}

        {step === 'questioning' && treatmentSession && (
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Question {(treatmentSession.total_questions || 3) - (treatmentSession.questions_remaining || 0)} of {treatmentSession.total_questions || 3}</CardTitle>
                <div className="text-sm text-muted-foreground">
                  {treatmentSession.questions_remaining || 0} remaining
                </div>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${((treatmentSession.total_questions || 3) - (treatmentSession.questions_remaining || 0)) / (treatmentSession.total_questions || 3) * 100}%`
                  }}
                ></div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {treatmentSession.current_question && (
                <div>
                  <h3 className="text-lg font-medium mb-4 text-card-foreground">
                    {treatmentSession.current_question.question}
                  </h3>

                  {treatmentSession.current_question.type === 'text' && (
                    <Input
                      value={currentAnswer}
                      onChange={(e) => setCurrentAnswer(e.target.value)}
                      placeholder="Type your answer here..."
                      className="w-full"
                    />
                  )}

                  {treatmentSession.current_question.type === 'number' && (
                    <Input
                      type="number"
                      value={currentAnswer}
                      onChange={(e) => setCurrentAnswer(e.target.value)}
                      placeholder="Enter a number..."
                      className="w-full"
                      min="1"
                      max="10"
                    />
                  )}

                  {treatmentSession.current_question.type === 'multiple_choice' && (
                    <div className="space-y-2">
                      {treatmentSession.current_question.options?.map((option, index) => (
                        <button
                          key={index}
                          onClick={() => handleOptionSelect(option)}
                          className={`w-full p-3 text-left border rounded-lg transition-colors ${
                            currentAnswer === option
                              ? 'border-primary bg-primary/10 text-primary'
                              : 'border-border hover:border-primary/50 bg-card text-card-foreground'
                          }`}
                        >
                          {option}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}

              <div className="flex space-x-3">
                <Button
                  onClick={submitAnswer}
                  disabled={!currentAnswer.trim() || loading}
                  loading={loading}
                  className="flex-1"
                >
                  {treatmentSession.questions_remaining === 1 ? 'Complete Assessment' : 'Next Question'}
                  <ArrowRightIcon className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 'completed' && treatmentSession && (
          <Card className="max-w-4xl mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle>Assessment Complete</CardTitle>
              <CardDescription>
                Based on your responses, here are your personalized treatment recommendations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 text-card-foreground">Treatment Recommendations</h3>
                <div className="prose max-w-none">
                  <div className="whitespace-pre-line text-card-foreground">
                    {treatmentSession.treatment}
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Important Note</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>These recommendations are AI-generated and should be used as guidance only. Please consult with a healthcare professional for proper diagnosis and treatment.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button onClick={resetSession} variant="outline" className="flex-1">
                  Start New Assessment
                </Button>
                <Button onClick={() => window.print()} className="flex-1">
                  Save/Print Results
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
