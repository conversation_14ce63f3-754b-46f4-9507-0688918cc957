'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/layout/Navbar';
import { Button } from '@/components/ui/Button';
import ClientOnly from '@/components/ClientOnly';
import { 
  HeartIcon, 
  ChatBubbleLeftIcon, 
  ClipboardDocumentListIcon,
  ShieldCheckIcon,
  SparklesIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

function HomeContent() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/treatment');
    }
  }, [isAuthenticated, router]);

  const features = [
    {
      name: 'AI-Powered Chat',
      description: 'Get instant health advice and treatment recommendations from our advanced AI assistant.',
      icon: ChatBubbleLeftIcon,
    },
    {
      name: 'Treatment Assessment',
      description: 'Upload images and answer questions to receive personalized treatment plans.',
      icon: ClipboardDocumentListIcon,
    },
    {
      name: 'Injury Prevention',
      description: 'Learn how to prevent injuries and maintain optimal health with expert guidance.',
      icon: ShieldCheckIcon,
    },
    {
      name: 'Personalized Care',
      description: 'Receive customized health recommendations based on your specific needs.',
      icon: SparklesIcon,
    },
    {
      name: 'Expert Knowledge',
      description: 'Access medical knowledge from certified professionals and trusted sources.',
      icon: UserGroupIcon,
    },
    {
      name: 'Health Monitoring',
      description: 'Track your health journey and monitor progress over time.',
      icon: HeartIcon,
    },
  ];

  if (isAuthenticated) {
    return null; // Will redirect to treatment
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:w-full lg:max-w-2xl lg:pb-28 xl:pb-32">
            <main className="mx-auto mt-10 max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">Your AI-Powered</span>{' '}
                  <span className="block text-blue-600 xl:inline">Health Assistant</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mx-auto sm:mt-5 sm:max-w-xl sm:text-lg md:mt-5 md:text-xl lg:mx-0">
                  Get instant health advice, treatment recommendations, and injury prevention tips from ATHLIX, 
                  your personal AI treatment assistant. Available 24/7 to help you stay healthy and active.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link href="/register">
                      <Button size="lg" className="w-full">
                        Get Started Free
                      </Button>
                    </Link>
                  </div>
                  <div className="mt-3 sm:ml-3 sm:mt-0">
                    <Link href="/login">
                      <Button variant="outline" size="lg" className="w-full">
                        Sign In
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-br from-blue-50 to-blue-100 sm:h-72 md:h-96 lg:h-full lg:w-full flex items-center justify-center">
            <div className="text-center">
              <HeartIcon className="mx-auto h-24 w-24 text-blue-600" />
              <p className="mt-4 text-lg font-medium text-blue-600">ATHLIX AI Assistant</p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-12 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base font-semibold uppercase tracking-wide text-blue-600">Features</h2>
            <p className="mt-2 text-3xl font-bold leading-8 tracking-tight text-gray-900 sm:text-4xl">
              Everything you need for better health
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              ATHLIX combines advanced AI technology with medical expertise to provide comprehensive health assistance.
            </p>
          </div>

          <div className="mt-10">
            <dl className="space-y-10 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10 md:space-y-0 lg:grid-cols-3">
              {features.map((feature) => (
                <div key={feature.name} className="relative">
                  <dt>
                    <div className="absolute flex h-12 w-12 items-center justify-center rounded-md bg-blue-500 text-white">
                      <feature.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                    <p className="ml-16 text-lg font-medium leading-6 text-gray-900">{feature.name}</p>
                  </dt>
                  <dd className="mt-2 ml-16 text-base text-gray-500">{feature.description}</dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600">
        <div className="mx-auto max-w-2xl py-16 px-4 text-center sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            <span className="block">Ready to improve your health?</span>
            <span className="block">Start your journey with ATHLIX today.</span>
          </h2>
          <p className="mt-4 text-lg leading-6 text-blue-200">
            Join thousands of users who trust ATHLIX for their health and wellness needs.
          </p>
          <Link href="/register">
            <Button size="lg" variant="secondary" className="mt-8">
              Create Your Account
            </Button>
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white">
        <div className="mx-auto max-w-7xl py-12 px-4 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <div className="flex justify-center space-x-6 md:order-2">
            <p className="text-center text-xs leading-5 text-gray-500">
              &copy; 2024 ATHLIX. All rights reserved.
            </p>
          </div>
          <div className="mt-8 md:order-1 md:mt-0">
            <div className="flex items-center justify-center md:justify-start">
              <HeartIcon className="h-6 w-6 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">ATHLIX</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default function Home() {
  return (
    <ClientOnly fallback={
      <div className="min-h-screen bg-white">
        <div className="animate-pulse">
          <div className="h-16 bg-gray-200"></div>
          <div className="max-w-7xl mx-auto px-4 py-20">
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-8"></div>
            <div className="h-10 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </div>
    }>
      <HomeContent />
    </ClientOnly>
  );
}
