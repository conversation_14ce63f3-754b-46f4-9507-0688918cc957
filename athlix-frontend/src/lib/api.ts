import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  created_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
}

export interface ChatMessage {
  session_id?: string;
  message: string;
  message_type?: string;
}

export interface ChatResponse {
  session_id: string;
  response: string;
  timestamp: string;
}

export interface ChatSession {
  session_id: string;
  message_count: number;
  last_activity: string | null;
}

export interface TreatmentQuestion {
  session_id: string;
  question_id: string;
  answer: string;
}

// Auth API
export const authAPI = {
  register: async (data: RegisterRequest) => {
    const response = await api.post('/register', data);
    return response.data;
  },

  login: async (data: LoginRequest) => {
    const response = await api.post('/login', data);
    return response.data;
  },

  getUsers: async () => {
    const response = await api.get('/users');
    return response.data;
  },
};

// Chat API
export const chatAPI = {
  createSession: async () => {
    const response = await api.post('/chat/new');
    return response.data;
  },

  sendMessage: async (data: ChatMessage) => {
    const response = await api.post('/chat/message', data);
    return response.data;
  },

  getSessions: async () => {
    const response = await api.get('/chat/sessions');
    return response.data;
  },

  getChatHistory: async (sessionId: string) => {
    const response = await api.get(`/chat/${sessionId}/history`);
    return response.data;
  },
};

// Treatment API
export const treatmentAPI = {
  startSession: async () => {
    const response = await api.post('/treatment/start');
    return response.data;
  },

  answerQuestion: async (data: TreatmentQuestion) => {
    const response = await api.post('/treatment/answer', data);
    return response.data;
  },

  getSession: async (sessionId: string) => {
    const response = await api.get(`/treatment/${sessionId}`);
    return response.data;
  },
};

// System API
export const systemAPI = {
  getHealth: async () => {
    const response = await api.get('/health');
    return response.data;
  },

  getInfo: async () => {
    const response = await api.get('/');
    return response.data;
  },
};

export default api;
