#!/usr/bin/env python3
"""
ATHLIX - Simple FastAPI Application
A simplified version that works with our current database setup
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, select
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional, List
import uvicorn
import uuid
import openai
import base64
import json

from database.models import Base, User, ChatSession, ChatMessage, TreatmentSession, PasswordResetToken, EmailVerificationToken
from config import settings
from pydantic import BaseModel, EmailStr
from datetime import datetime

# Simple Pydantic models
class UserCreate(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: EmailStr
    is_active: bool
    is_verified: bool
    created_at: datetime

class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    expires_in: int

# Chat-related models
class ChatSessionResponse(BaseModel):
    session_id: str
    title: Optional[str] = None
    message_count: int = 0
    last_activity: Optional[datetime] = None
    created_at: datetime

class ChatMessageResponse(BaseModel):
    message_id: str
    role: str
    content: str
    timestamp: datetime

class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    message_id: str
    session_id: str
    response: str
    timestamp: datetime

# Treatment-related models
class TreatmentSessionResponse(BaseModel):
    session_id: str
    status: str
    message: Optional[str] = None
    ai_response: Optional[str] = None
    conversation_history: Optional[List[dict]] = None
    created_at: Optional[datetime] = None

class TreatmentMessageRequest(BaseModel):
    session_id: str
    message: str

class TreatmentMessageResponse(BaseModel):
    session_id: str
    ai_response: str
    conversation_history: List[dict]

class ImageUploadRequest(BaseModel):
    image_data: str  # Base64 encoded image
    session_id: Optional[str] = None

# Auth-related models
class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str

class VerifyEmailRequest(BaseModel):
    token: str

# Database setup
engine = create_engine(settings.database_url, echo=settings.debug)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Security
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="signin")

# Initialize OpenAI client
openai.api_key = settings.openai_api_key

# GPT Helper Functions
async def analyze_image_and_start_conversation(image_data: str) -> str:
    """Analyze uploaded image and start a medical conversation"""
    try:
        # Remove data URL prefix if present
        if image_data.startswith('data:image'):
            image_data = image_data.split(',')[1]

        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": """You are ATHLIX, a professional AI medical assistant. You help patients by analyzing their medical images and having natural conversations about their health concerns.

When a patient uploads an image:
1. Analyze what you can see in the image
2. Start a natural, empathetic conversation
3. Ask open-ended questions to understand their condition better
4. Be professional but conversational
5. Always remind them this is for informational purposes only

Do NOT ask multiple choice questions or yes/no questions. Have a natural conversation like a real doctor would."""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "I've uploaded an image of my condition. Can you help me understand what might be going on and what I should do?"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=500
        )

        return response.choices[0].message.content
    except Exception as e:
        print(f"Error analyzing image: {e}")
        return "I can see you've uploaded an image. Could you tell me more about what's bothering you? What symptoms are you experiencing and how long have you had them?"

async def continue_medical_conversation(conversation_history: List[dict], user_message: str) -> str:
    """Continue the medical conversation with GPT"""
    try:
        messages = [
            {
                "role": "system",
                "content": """You are ATHLIX, a professional AI medical assistant. Continue this natural conversation with the patient.

Guidelines:
- Be empathetic and professional
- Ask follow-up questions naturally
- Provide helpful information and suggestions
- Always remind patients to consult healthcare professionals for serious concerns
- Keep responses conversational, not clinical
- Don't ask multiple choice or yes/no questions
- Ask open-ended questions to gather more information

Remember: This is for informational purposes only and should not replace professional medical advice."""
            }
        ]

        # Add conversation history
        messages.extend(conversation_history)

        # Add current user message
        messages.append({
            "role": "user",
            "content": user_message
        })

        response = openai.chat.completions.create(
            model="gpt-4",
            messages=messages,
            max_tokens=500,
            temperature=0.7
        )

        return response.choices[0].message.content
    except Exception as e:
        print(f"Error in conversation: {e}")
        return "I understand. Could you tell me more about how you're feeling and what specific symptoms you're experiencing?"

app = FastAPI(
    title="ATHLIX API",
    description="AI-powered health and treatment assistant",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Security functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm="HS256")
    return encoded_jwt

def get_user_by_email(db: Session, email: str):
    return db.execute(select(User).where(User.email == email)).scalar_one_or_none()

def get_user_by_id(db: Session, user_id: int):
    return db.execute(select(User).where(User.id == user_id)).scalar_one_or_none()

def authenticate_user(db: Session, email: str, password: str):
    user = get_user_by_email(db, email)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=["HS256"])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = get_user_by_id(db, user_id=int(user_id))
    if user is None:
        raise credentials_exception
    return user

# Routes
@app.get("/")
async def root():
    return {
        "message": "Welcome to ATHLIX API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/signup", response_model=dict)
async def signup(user: UserCreate, db: Session = Depends(get_db)):
    # Check if user already exists
    db_user = get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = User(
        first_name=user.first_name,
        last_name=user.last_name,
        email=user.email,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=False
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return {"message": "User created successfully", "user_id": db_user.id}

@app.post("/signin", response_model=Token)
async def signin(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user.id,
        "expires_in": settings.access_token_expire_minutes * 60
    }

@app.get("/users/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return UserResponse(
        id=current_user.id,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        email=current_user.email,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        created_at=current_user.created_at
    )

@app.post("/forgot-password", response_model=dict)
async def forgot_password(request: ForgotPasswordRequest, db: Session = Depends(get_db)):
    user = get_user_by_email(db, email=request.email)
    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If the email exists, a password reset link has been sent"}

    # Generate reset token
    reset_token = str(uuid.uuid4())
    expires_at = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour

    # Save reset token to database
    db_token = PasswordResetToken(
        user_id=user.id,
        token=reset_token,
        expires_at=expires_at,
        is_used=False
    )
    db.add(db_token)
    db.commit()

    # In a real implementation, you would send an email here
    # For demo purposes, we'll return the token (don't do this in production!)
    return {
        "message": "Password reset token generated",
        "token": reset_token,  # Remove this in production
        "note": "In production, this token would be sent via email"
    }

@app.post("/reset-password", response_model=dict)
async def reset_password(request: ResetPasswordRequest, db: Session = Depends(get_db)):
    # Find valid reset token
    token_record = db.execute(
        select(PasswordResetToken)
        .where(PasswordResetToken.token == request.token)
        .where(PasswordResetToken.is_used == False)
        .where(PasswordResetToken.expires_at > datetime.utcnow())
    ).scalar_one_or_none()

    if not token_record:
        raise HTTPException(status_code=400, detail="Invalid or expired reset token")

    # Get user
    user = get_user_by_id(db, token_record.user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update password
    user.hashed_password = get_password_hash(request.new_password)

    # Mark token as used
    token_record.is_used = True

    db.commit()

    return {"message": "Password reset successfully"}

@app.post("/send-verification-email", response_model=dict)
async def send_verification_email(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    if current_user.is_verified:
        return {"message": "Email is already verified"}

    # Generate verification token
    verification_token = str(uuid.uuid4())
    expires_at = datetime.utcnow() + timedelta(hours=24)  # Token expires in 24 hours

    # Save verification token to database
    db_token = EmailVerificationToken(
        user_id=current_user.id,
        token=verification_token,
        expires_at=expires_at,
        is_used=False
    )
    db.add(db_token)
    db.commit()

    # In a real implementation, you would send an email here
    return {
        "message": "Verification email sent",
        "token": verification_token,  # Remove this in production
        "note": "In production, this token would be sent via email"
    }

@app.post("/verify-email", response_model=dict)
async def verify_email(request: VerifyEmailRequest, db: Session = Depends(get_db)):
    # Find valid verification token
    token_record = db.execute(
        select(EmailVerificationToken)
        .where(EmailVerificationToken.token == request.token)
        .where(EmailVerificationToken.is_used == False)
        .where(EmailVerificationToken.expires_at > datetime.utcnow())
    ).scalar_one_or_none()

    if not token_record:
        raise HTTPException(status_code=400, detail="Invalid or expired verification token")

    # Get user
    user = get_user_by_id(db, token_record.user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Mark email as verified
    user.is_verified = True

    # Mark token as used
    token_record.is_used = True

    db.commit()

    return {"message": "Email verified successfully"}

# Chat endpoints
@app.get("/chat/sessions", response_model=List[ChatSessionResponse])
async def get_chat_sessions(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    sessions = db.execute(
        select(ChatSession)
        .where(ChatSession.user_id == current_user.id)
        .order_by(ChatSession.created_at.desc())
    ).scalars().all()

    result = []
    for session in sessions:
        # Count messages in this session
        messages = db.execute(
            select(ChatMessage)
            .where(ChatMessage.session_id == session.id)
        ).scalars().all()

        result.append(ChatSessionResponse(
            session_id=session.session_id,
            title=session.title,
            message_count=len(messages),
            last_activity=session.updated_at,
            created_at=session.created_at
        ))

    return result

@app.post("/chat/new", response_model=dict)
async def create_chat_session(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    session_id = str(uuid.uuid4())

    db_session = ChatSession(
        user_id=current_user.id,
        session_id=session_id,
        title="New Chat",
        is_active=True,
        session_type="chat"
    )
    db.add(db_session)
    db.commit()
    db.refresh(db_session)

    return {"session_id": session_id}

@app.post("/chat/message", response_model=ChatResponse)
async def send_chat_message(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Get or create session
    if request.session_id:
        session = db.execute(
            select(ChatSession)
            .where(ChatSession.session_id == request.session_id)
            .where(ChatSession.user_id == current_user.id)
        ).scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
    else:
        # Create new session
        session_id = str(uuid.uuid4())
        session = ChatSession(
            user_id=current_user.id,
            session_id=session_id,
            title="New Chat",
            is_active=True,
            session_type="chat"
        )
        db.add(session)
        db.commit()
        db.refresh(session)

    # Save user message
    user_message_id = str(uuid.uuid4())
    user_message = ChatMessage(
        session_id=session.id,
        message_id=user_message_id,
        role="user",
        content=request.message,
        message_type="text"
    )
    db.add(user_message)

    # Get conversation history for this session
    existing_messages = db.execute(
        select(ChatMessage)
        .where(ChatMessage.session_id == session.id)
        .order_by(ChatMessage.created_at.asc())
    ).scalars().all()

    # Build conversation history for GPT
    conversation_history = []
    for msg in existing_messages:
        conversation_history.append({
            "role": msg.role,
            "content": msg.content
        })

    # Generate AI response using GPT
    ai_response = await continue_medical_conversation(conversation_history, request.message)

    # Save AI response
    ai_message_id = str(uuid.uuid4())
    ai_message = ChatMessage(
        session_id=session.id,
        message_id=ai_message_id,
        role="assistant",
        content=ai_response,
        message_type="text"
    )
    db.add(ai_message)

    # Update session
    session.updated_at = datetime.utcnow()

    db.commit()

    return ChatResponse(
        message_id=ai_message_id,
        session_id=session.session_id,
        response=ai_response,
        timestamp=ai_message.created_at
    )

@app.get("/chat/{session_id}/history", response_model=dict)
async def get_chat_history(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Get session
    session = db.execute(
        select(ChatSession)
        .where(ChatSession.session_id == session_id)
        .where(ChatSession.user_id == current_user.id)
    ).scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Get messages
    messages = db.execute(
        select(ChatMessage)
        .where(ChatMessage.session_id == session.id)
        .order_by(ChatMessage.created_at.asc())
    ).scalars().all()

    return {
        "messages": [
            {
                "message_id": msg.message_id,
                "role": msg.role,
                "content": msg.content,
                "created_at": msg.created_at.isoformat()
            }
            for msg in messages
        ]
    }

# Treatment endpoints
@app.post("/treatment/start", response_model=dict)
async def start_treatment_session(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    session_id = str(uuid.uuid4())

    # Create session in "image_upload" status - user must upload image first
    db_session = TreatmentSession(
        user_id=current_user.id,
        session_id=session_id,
        questions=[],
        answers=[],
        status="image_upload"
    )
    db.add(db_session)
    db.commit()
    db.refresh(db_session)

    return {
        "session_id": session_id,
        "status": "image_upload",
        "message": "Please upload an image of the affected area to begin your consultation with our AI medical assistant"
    }

@app.post("/treatment/upload-image", response_model=dict)
async def upload_treatment_image(
    request: ImageUploadRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Get or create session
    if request.session_id:
        session = db.execute(
            select(TreatmentSession)
            .where(TreatmentSession.session_id == request.session_id)
            .where(TreatmentSession.user_id == current_user.id)
        ).scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="Treatment session not found")
    else:
        # Create new session if none provided
        session_id = str(uuid.uuid4())
        session = TreatmentSession(
            user_id=current_user.id,
            session_id=session_id,
            questions=[],
            answers=[],
            status="image_upload"
        )
        db.add(session)
        db.commit()
        db.refresh(session)

    # Save image data
    session.image_data = request.image_data
    session.status = "conversation"
    session.updated_at = datetime.utcnow()

    # Analyze image with GPT and start conversation
    ai_response = await analyze_image_and_start_conversation(request.image_data)

    # Initialize conversation history
    conversation_history = [
        {
            "role": "assistant",
            "content": ai_response
        }
    ]

    # Store conversation in session
    session.questions = conversation_history  # Reusing questions field for conversation history

    db.commit()

    return {
        "session_id": session.session_id,
        "status": "conversation",
        "message": "Image uploaded successfully. Our AI has analyzed your image and started the consultation.",
        "ai_response": ai_response,
        "conversation_history": conversation_history
    }

@app.post("/treatment/message", response_model=dict)
async def send_treatment_message(
    request: TreatmentMessageRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Get session
    session = db.execute(
        select(TreatmentSession)
        .where(TreatmentSession.session_id == request.session_id)
        .where(TreatmentSession.user_id == current_user.id)
    ).scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Treatment session not found")

    # Get conversation history
    conversation_history = session.questions or []

    # Add user message to conversation
    conversation_history.append({
        "role": "user",
        "content": request.message
    })

    # Get AI response
    ai_response = await continue_medical_conversation(conversation_history, request.message)

    # Add AI response to conversation
    conversation_history.append({
        "role": "assistant",
        "content": ai_response
    })

    # Update session
    session.questions = conversation_history
    session.updated_at = datetime.utcnow()
    db.commit()

    return {
        "session_id": request.session_id,
        "status": "conversation",
        "ai_response": ai_response,
        "conversation_history": conversation_history
    }

@app.get("/treatment/{session_id}", response_model=dict)
async def get_treatment_session(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    session = db.execute(
        select(TreatmentSession)
        .where(TreatmentSession.session_id == session_id)
        .where(TreatmentSession.user_id == current_user.id)
    ).scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Treatment session not found")

    questions = session.questions or []
    answers = session.answers or []

    if session.status == "completed":
        return {
            "session_id": session_id,
            "status": "completed",
            "treatment": session.treatment_recommendation,
            "questions_remaining": 0,
            "total_questions": len(questions)
        }
    else:
        # Still in questioning phase
        current_question_index = len(answers)
        if current_question_index < len(questions):
            current_question = questions[current_question_index]
            return {
                "session_id": session_id,
                "status": "questioning",
                "current_question": {
                    "question": current_question["question"],
                    "options": current_question["options"],
                    "question_number": current_question_index + 1,
                    "total_questions": len(questions)
                },
                "questions_remaining": len(questions) - len(answers),
                "total_questions": len(questions)
            }
        else:
            # Should not happen, but handle gracefully
            return {
                "session_id": session_id,
                "status": "error",
                "message": "Session in invalid state"
            }

if __name__ == "__main__":
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Run the application
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
